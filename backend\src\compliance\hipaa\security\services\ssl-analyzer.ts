import ssl<PERSON>hecker from 'ssl-checker';
import { connect } from 'tls';
import { SSLAnalysisResult, SSLVulnerability } from '../types';

export class SSLAnalyzer {
  async analyzeDomain(hostname: string, port: number = 443): Promise<SSLAnalysisResult> {
    try {
      console.log(`🔒 Starting SSL analysis for ${hostname}:${port}`);

      // Basic SSL certificate check with timeout and error handling
      let certInfo: {
        valid: boolean;
        daysRemaining?: number;
        issuer?: string;
        subject?: string;
      };
      try {
        certInfo = await Promise.race([
          sslChecker(hostname, { method: 'GET', port }) as Promise<{
            valid: boolean;
            daysRemaining?: number;
            issuer?: string;
            subject?: string;
          }>,
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('SSL checker timeout')), 10000),
          ),
        ]);
        console.log(`✅ SSL certificate check completed for ${hostname}`);
      } catch (certError) {
        console.warn(
          `⚠️ SSL certificate check failed for ${hostname}:`,
          certError instanceof Error ? certError.message : 'Unknown error',
        );
        // Provide fallback certificate info
        certInfo = {
          valid: false,
          daysRemaining: 0,
          issuer: 'Unknown - SSL check failed',
          subject: hostname,
        };
      }

      // Advanced TLS configuration check with error handling
      let tlsInfo: {
        version: string;
        cipher: string;
        keyExchange: string;
        serverSignature: string;
        supportedProtocols: string[];
      };
      try {
        tlsInfo = await this.checkTLSConfiguration(hostname, port);
        console.log(`✅ TLS configuration check completed for ${hostname}`);
      } catch (tlsError) {
        console.warn(
          `⚠️ TLS configuration check failed for ${hostname}:`,
          tlsError instanceof Error ? tlsError.message : 'Unknown error',
        );
        // Provide fallback TLS info
        tlsInfo = {
          version: 'unknown',
          cipher: 'unknown',
          keyExchange: 'unknown',
          serverSignature: 'unknown',
          supportedProtocols: ['unknown'],
        };
      }

      // Vulnerability assessment
      const vulnerabilities = await this.assessVulnerabilities(hostname, port, tlsInfo);

      // HIPAA compliance check
      const hipaaCompliant = this.assessHIPAACompliance(certInfo, tlsInfo, vulnerabilities);

      // Calculate grade
      const grade = this.calculateSSLGrade(certInfo, tlsInfo, vulnerabilities);

      console.log(
        `🔒 SSL analysis completed for ${hostname} - Grade: ${grade}, HIPAA Compliant: ${hipaaCompliant}`,
      );

      return {
        isValid: certInfo.valid,
        daysRemaining: certInfo.daysRemaining || 0,
        issuer: (certInfo as { issuer?: string }).issuer || 'Unknown',
        subject: (certInfo as { subject?: string }).subject || hostname,
        tlsVersion: tlsInfo.version,
        cipherSuite: tlsInfo.cipher,
        keyExchange: tlsInfo.keyExchange,
        serverSignature: tlsInfo.serverSignature,
        hipaaCompliant,
        vulnerabilities,
        grade,
      };
    } catch (error) {
      console.error(
        `❌ SSL analysis failed for ${hostname}:`,
        error instanceof Error ? error.message : 'Unknown error',
      );
      throw new Error(
        `SSL analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  private async checkTLSConfiguration(
    hostname: string,
    port: number,
  ): Promise<{
    version: string;
    cipher: string;
    keyExchange: string;
    serverSignature: string;
    supportedProtocols: string[];
  }> {
    return new Promise((resolve, reject) => {
      console.log(`🔐 Checking TLS configuration for ${hostname}:${port}`);

      const socket = connect({
        host: hostname,
        port,
        secureProtocol: 'TLSv1_2_method', // Start with TLS 1.2
        timeout: 8000, // 8 second timeout
      });

      let resolved = false;

      socket.on('secureConnect', () => {
        if (resolved) return;
        resolved = true;

        try {
          const cipher = socket.getCipher();
          const protocol = socket.getProtocol();
          const peerCertificate = socket.getPeerCertificate();

          console.log(
            `✅ TLS connection established for ${hostname} - Protocol: ${protocol}, Cipher: ${cipher?.name}`,
          );

          resolve({
            version: protocol || 'unknown',
            cipher: cipher?.name || 'unknown',
            keyExchange: cipher?.version || 'unknown',
            serverSignature: peerCertificate?.fingerprint || 'unknown',
            supportedProtocols: [protocol || 'unknown'],
          });
        } catch (error) {
          console.warn(
            `⚠️ Error extracting TLS info for ${hostname}:`,
            error instanceof Error ? error.message : 'Unknown error',
          );
          resolve({
            version: 'unknown',
            cipher: 'unknown',
            keyExchange: 'unknown',
            serverSignature: 'unknown',
            supportedProtocols: ['unknown'],
          });
        } finally {
          socket.destroy();
        }
      });

      socket.on('error', (error: Error) => {
        if (resolved) return;
        resolved = true;
        console.warn(`⚠️ TLS connection error for ${hostname}:`, error.message);
        socket.destroy();
        reject(error);
      });

      socket.setTimeout(8000, () => {
        if (resolved) return;
        resolved = true;
        console.warn(`⚠️ TLS connection timeout for ${hostname}`);
        socket.destroy();
        reject(new Error('TLS connection timeout'));
      });
    });
  }

  private async assessVulnerabilities(
    _hostname: string,
    _port: number,
    tlsInfo: { version: string; cipher: string },
  ): Promise<SSLVulnerability[]> {
    const vulnerabilities: SSLVulnerability[] = [];

    // Check for weak TLS versions
    if (tlsInfo.version.includes('1.0') || tlsInfo.version.includes('1.1')) {
      vulnerabilities.push({
        type: 'weak_tls_version',
        severity: 'high',
        description: `Weak TLS version detected: ${tlsInfo.version}`,
        remediation: 'Upgrade to TLS 1.2 or higher',
      });
    }

    // Check for weak ciphers
    const weakCiphers = ['RC4', 'DES', '3DES', 'MD5'];
    if (weakCiphers.some((weak) => tlsInfo.cipher.includes(weak))) {
      vulnerabilities.push({
        type: 'weak_cipher',
        severity: 'medium',
        description: `Weak cipher suite detected: ${tlsInfo.cipher}`,
        remediation: 'Configure strong cipher suites (AES-256, ChaCha20)',
      });
    }

    return vulnerabilities;
  }

  private assessHIPAACompliance(
    certInfo: { valid: boolean; daysRemaining?: number },
    tlsInfo: { version: string; cipher: string },
    vulnerabilities: SSLVulnerability[],
  ): boolean {
    // HIPAA requires valid certificates
    if (!certInfo.valid) return false;

    // Certificate should not expire soon
    if ((certInfo.daysRemaining || 0) < 30) return false;

    // Must use TLS 1.2 or higher
    if (!tlsInfo.version.includes('1.2') && !tlsInfo.version.includes('1.3')) return false;

    // No critical or high vulnerabilities
    const criticalVulns = vulnerabilities.filter(
      (v) => v.severity === 'critical' || v.severity === 'high',
    );
    if (criticalVulns.length > 0) return false;

    // Must use strong encryption
    if (!tlsInfo.cipher.includes('AES') && !tlsInfo.cipher.includes('ChaCha20')) return false;

    return true;
  }

  private calculateSSLGrade(
    certInfo: { valid: boolean; daysRemaining?: number },
    tlsInfo: { version: string; cipher: string },
    vulnerabilities: SSLVulnerability[],
  ): string {
    let score = 100;

    // Deduct for invalid certificate
    if (!certInfo.valid) score -= 50;

    // Deduct for expiring certificate
    if ((certInfo.daysRemaining || 0) < 30) score -= 20;

    // Deduct for weak TLS version
    if (tlsInfo.version.includes('1.0') || tlsInfo.version.includes('1.1')) score -= 30;

    // Deduct for vulnerabilities
    vulnerabilities.forEach((vuln) => {
      switch (vuln.severity) {
        case 'critical':
          score -= 40;
          break;
        case 'high':
          score -= 20;
          break;
        case 'medium':
          score -= 10;
          break;
        case 'low':
          score -= 5;
          break;
      }
    });

    // Convert score to grade
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }
}
