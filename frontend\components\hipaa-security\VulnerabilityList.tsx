import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  ChevronDown,
  ChevronRight,
  AlertTriangle,
  Shield,
  MapPin,
  ExternalLink,
  Info,
} from 'lucide-react';
import { VulnerabilityResult, Severity } from '@/types/hipaa-security';

interface VulnerabilityListProps {
  vulnerabilities: VulnerabilityResult[];
}

export const VulnerabilityList: React.FC<VulnerabilityListProps> = ({ vulnerabilities }) => {
  const [expandedVulns, setExpandedVulns] = useState<Set<string>>(new Set());

  const toggleExpanded = (vulnId: string) => {
    const newExpanded = new Set(expandedVulns);
    if (newExpanded.has(vulnId)) {
      newExpanded.delete(vulnId);
    } else {
      newExpanded.add(vulnId);
    }
    setExpandedVulns(newExpanded);
  };

  const getSeverityColor = (severity: Severity): string => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500 text-white';
      case 'high':
        return 'bg-orange-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-black';
      case 'low':
        return 'bg-blue-500 text-white';
      case 'info':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getSeverityIcon = (severity: Severity) => {
    switch (severity) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'medium':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'low':
        return <Shield className="h-5 w-5 text-blue-500" />;
      case 'info':
        return <Info className="h-5 w-5 text-gray-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getBorderColor = (severity: Severity): string => {
    switch (severity) {
      case 'critical':
        return 'border-red-200';
      case 'high':
        return 'border-orange-200';
      case 'medium':
        return 'border-yellow-200';
      case 'low':
        return 'border-blue-200';
      case 'info':
        return 'border-gray-200';
      default:
        return 'border-gray-200';
    }
  };

  if (vulnerabilities.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <Shield className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-green-700 mb-2">No Vulnerabilities Found</h3>
          <p className="text-gray-500">
            Great! No security vulnerabilities were detected during the scan.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Group vulnerabilities by severity
  const groupedVulns = vulnerabilities.reduce(
    (acc, vuln) => {
      if (!acc[vuln.severity]) {
        acc[vuln.severity] = [];
      }
      acc[vuln.severity].push(vuln);
      return acc;
    },
    {} as Record<Severity, VulnerabilityResult[]>,
  );

  const severityOrder: Severity[] = ['critical', 'high', 'medium', 'low', 'info'];

  return (
    <div className="space-y-6">
      {severityOrder.map((severity) => {
        const vulns = groupedVulns[severity];
        if (!vulns || vulns.length === 0) return null;

        return (
          <div key={severity}>
            <div className="flex items-center gap-3 mb-6">
              {getSeverityIcon(severity)}
              <h3 className="text-xl font-bold text-gray-900 capitalize">{severity} Severity</h3>
              <Badge className={`${getSeverityColor(severity)} text-white font-semibold px-3 py-1`}>
                {vulns.length} {vulns.length === 1 ? 'issue' : 'issues'}
              </Badge>
            </div>

            <div className="space-y-3">
              {vulns.map((vuln) => {
                const isExpanded = expandedVulns.has(vuln.id);

                return (
                  <Card key={vuln.id} className={getBorderColor(vuln.severity)}>
                    <Collapsible>
                      <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-gray-50">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              {getSeverityIcon(vuln.severity)}
                              <div>
                                <CardTitle className="text-lg font-bold text-gray-900 leading-tight">
                                  {vuln.type}
                                </CardTitle>
                                <p className="text-sm text-gray-600 mt-1 mb-3 leading-relaxed">
                                  {vuln.description}
                                </p>
                                <div className="flex items-center gap-2 mt-2">
                                  <Badge
                                    className={`${getSeverityColor(vuln.severity)} text-white font-semibold px-2 py-1`}
                                  >
                                    {vuln.severity.toUpperCase()}
                                  </Badge>
                                  {vuln.cweId && (
                                    <Badge variant="outline" className="font-medium px-2 py-1">
                                      CWE-{vuln.cweId}
                                    </Badge>
                                  )}
                                  {vuln.owaspCategory && (
                                    <Badge variant="secondary" className="font-medium px-2 py-1">
                                      {vuln.owaspCategory}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleExpanded(vuln.id)}
                            >
                              {isExpanded ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <ChevronRight className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </CardHeader>
                      </CollapsibleTrigger>

                      <CollapsibleContent>
                        <CardContent>
                          <div className="space-y-6">
                            <div className="bg-gray-50 border border-gray-200 p-4 rounded-lg">
                              <h5 className="font-bold text-gray-800 mb-3 flex items-center gap-2">
                                <MapPin className="h-5 w-5" />
                                Location
                              </h5>
                              <code className="text-sm bg-white border border-gray-300 px-3 py-2 rounded font-mono leading-relaxed block">
                                {vuln.location}
                              </code>
                            </div>

                            {Object.keys(vuln.evidence).length > 0 && (
                              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                                <h5 className="font-bold text-blue-800 mb-3 flex items-center gap-2">
                                  <Info className="h-5 w-5" />
                                  Evidence Details
                                </h5>
                                <div className="bg-white border border-blue-200 p-4 rounded text-sm">
                                  <pre className="whitespace-pre-wrap overflow-x-auto font-mono leading-relaxed text-gray-800">
                                    {JSON.stringify(vuln.evidence, null, 2)}
                                  </pre>
                                </div>
                              </div>
                            )}

                            <div className="bg-blue-50 p-4 rounded">
                              <h5 className="font-medium text-blue-800 mb-2">
                                Remediation Guidance
                              </h5>
                              <p className="text-blue-700">{vuln.remediationGuidance}</p>
                            </div>

                            {(vuln.cweId || vuln.owaspCategory) && (
                              <div className="flex gap-4 text-sm">
                                {vuln.cweId && (
                                  <div>
                                    <span className="font-medium">CWE Reference:</span>
                                    <a
                                      href={`https://cwe.mitre.org/data/definitions/${vuln.cweId}.html`}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-blue-600 hover:underline ml-1 inline-flex items-center gap-1"
                                    >
                                      CWE-{vuln.cweId}
                                      <ExternalLink className="h-3 w-3" />
                                    </a>
                                  </div>
                                )}
                                {vuln.owaspCategory && (
                                  <div>
                                    <span className="font-medium">OWASP Category:</span>
                                    <span className="ml-1">{vuln.owaspCategory}</span>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </CollapsibleContent>
                    </Collapsible>
                  </Card>
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
};
