// HIPAA Privacy Components - Frontend Implementation
export { HipaaPrivacyResultsPage } from './HipaaPrivacyResultsPage';
export { CheckResultsList } from './CheckResultsList';
export { RecommendationsList } from './RecommendationsList';
export { ComplianceSummary } from './ComplianceSummary';
export { SectionBreakdown } from './SectionBreakdown';
export { ContentAnalysisDisplay } from './ContentAnalysisDisplay';

// Re-export types for convenience
export type {
  HipaaPrivacyScanResult,
  HipaaPrivacyCheckResult,
  HipaaPrivacyFinding,
  HipaaPrivacyEvidence,
  HipaaPrivacyRecommendation,
  HipaaPrivacyScanSummary,
  HipaaPrivacyScanMetadata,
  HipaaPrivacyRiskLevel,
  HipaaPrivacySeverity,
  HipaaPrivacyPriority,
  HipaaPrivacyEffort,
  HipaaPrivacyImpact,
  HipaaPrivacyScanStatus,
  HipaaPrivacyCheckCategory,
  HipaaPrivacyFindingType,
  HipaaPrivacyResultsPageProps,
  CheckResultCardProps,
  RecommendationCardProps,
  ComplianceSummaryProps,
  SectionBreakdownProps,
  ContentAnalysisProps,
  HipaaPrivacyScanConfig,
} from '@/types/hipaa-privacy';
