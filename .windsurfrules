# Compliance Checker .cursorrules
# Project-specific rules for Cursor AI to ensure a stable, secure, and maintainable codebase

# Project Context
project:
  name: Comply Checker
  domain: ComplyChecker.com
  description: SaaS MVP for GDPR/CCPA compliance scanning, targeting SMBs
  stack:
    - frontend: typescript, nextjs@14.x, react@18.x, tailwindcss
    - backend: typescript, express@4.x, node@20.x
    - database: postgresql@16.x
    - integrations: keycloak, listmonk, n8n
    - ui: shadcn-ui, tailwindcss
  environment:
    - setup: local windows, wsl2, docker-desktop
    - tools: install Node.js, Docker Desktop, PostgreSQL
    - editor: use VS Code with TypeScript, ESLint, Prettier extensions
    - integrations: run via Docker (Keycloak, Listmonk, n8n)
    - email: test with MailHog
    - staging:
      - use docker-compose -f docker-compose.staging.yml up
      - env: .env.staging with placeholders for Keycloak (realm), Listmonk (SMTP), n8n (webhook URLs)
      - defaults: provide .env.example.staging with safe values (e.g., KEYCLOAK_REALM=demo)
      - validation: use single /lib/env.ts file with <PERSON>od to check .env variables at startup
      - example: provide .env.example with all required variables
  deployment: contabo-vps, docker
  constraints:
    - non-technical user relying on Cursor
    - prioritize free/open-source tools
    - GDPR/CCPA compliance
  goals:
    - page load time < 2s
    - scan time < 5s
    - secure APIs and auth
    - seamless frontend-backend sync

# Tech Stack & File Structure
structure:
  root:
    - frontend/:
      - app/:
        - layout.tsx: Root layout
        - page.tsx: Home page
        - auth/: Auth routes
        - dashboard/: Dashboard routes
        - compliance/: Compliance routes
          - hipaa/: HIPAA-specific pages
          - gdpr/: GDPR-specific pages
      - components/:
        - ui/: UI components
        - compliance/: Compliance components
          - hipaa/: HIPAA-specific components
          - gdpr/: GDPR-specific components
        - layout/: Layout components
      - lib/:
        - api.ts: API client
      - styles/: Styles
    - backend/:
      - routes/:
        - index.ts: Main router
        - health.ts: Health check routes
        - auth.ts: Authentication routes
        - compliance/: Compliance routes
          - index.ts: Compliance router
          - hipaa.ts: HIPAA-specific routes
          - gdpr.ts: GDPR-specific routes
      - compliance/:
        - hipaa/: HIPAA compliance module (flat file structure)
          - access-control.ts: Access control checks
          - audit-controls.ts: Audit controls checks
          - types.ts: Type definitions
          - index.ts: Main export
        - gdpr/: GDPR compliance module
      - utils/:
        - logger.ts: Logging utility
      - services/: Service integrations
      - types/: Shared types
      - lib/: Library code
      - config/: Configuration
      - index.ts: Main entry point
    - migrations/: Database migrations
    - docs/: Documentation
    - docker/: Docker configuration
    - package.json: NPM package file
    - BUGS.md: Bug tracking
  naming:
    - pages: kebab-case (e.g., sign-up.tsx)
    - components: PascalCase (e.g., ComplianceScore.tsx)
    - routes: kebab-case (e.g., scan-compliance.ts)
    - utils: kebab-case (e.g., scrape-rules.ts)
    - migrations: YYYYMMDDHHMMSS_description.sql (e.g., 20250505120000_create_users.sql)
    - seeds: [env]_[resource].js (e.g., test_users.js, demo_compliance_rules.js)

# Language and Framework Rules
language: typescript
framework:
  - nextjs
  - express
  - postgresql
  - knex

# Code Style & Best Practices
coding:
  # TypeScript
  - use strict typing in tsconfig.json
  - avoid using 'any'; define precise interfaces/types
  - use camelCase for variables, functions, methods
  - use PascalCase for classes, interfaces, types
  - use kebab-case for file/directory names
  - start function names with verbs (e.g., getUser, scanCompliance)
  - use JSDoc for public classes/methods
  - one export per file
  - avoid magic numbers; define constants
  # Functional Programming
  - prefer functional components and hooks over classes
  - keep components pure and declarative
  # Reusability
  - prioritize reusable components/services
  - maintain single-responsibility principle
  # Validation
  - use Zod for runtime input validation
  - infer types from Zod schemas
  # Compliance Module Structure
  - use flat file structure for all compliance modules
  - organize by feature with one file per feature (e.g., access-control.ts)
  - maintain shared types.ts file in each module
  - aggregate exports through index.ts
  - avoid nested directories within modules
  - minimize import complexity
  - ensure direct validation in route handlers (no middleware)

# CI/CD
ci:
  - use GitHub Actions
  - steps:
    - build: npm run build
    - test: npm run test -- --silent, ensure >90% coverage
    - security: npm audit, fail on high/critical vulnerabilities
    - health-check: curl http://localhost:3000/api/v1/health, verify 200 status, <200ms response, payload { status: string, uptime: number, database: boolean }
  - trigger: push/pull_request on main, feature/*, hotfix/*
  - artifacts: store test coverage reports, health-check logs
deployment:
  - target: Contabo VPS, Docker
  - strategy: deploy latest tagged image (e.g., v1.0.0)
  - rollback: redeploy previous image (e.g., v1.0.0-rc.1)
  - env: manage via .env files in Docker, no secrets in repo

# Bug Tracking
bug-tracking:
  - file: single BUGS.md in root
  - format:
    - id: unique identifier (e.g., BUG-001)
    - description: brief summary of the bug
    - root-cause: technical explanation
    - fix: solution applied
    - rule-reference: link to .cursorrules section (e.g., coding:typescript:avoid any)
  - process: log bugs during development/testing, commit with fixes
  - maintenance: archive resolved bugs to BUGS_ARCHIVE.md after releases
  - ci: lint BUGS.md for format consistency using markdownlint with rules for required fields (id, description, root-cause, fix, rule-reference)

# Database Migrations
migrations:
  - tool: knex
  - folder: /migrations
  - naming: YYYYMMDDHHMMSS_description.sql
  - seeds:
    - test: /migrations/seeds/test, for Jest/Playwright
    - demo: /migrations/seeds/demo, for realistic demo data
  - documentation: create single README.md in /migrations/seeds
    - explain npm run seed:test (for tests) vs npm run seed:demo (for demos)
    - use non-technical language
  - rollback: manual revert to previous migration
  - version: pin knex to ^3.x, postgresql to 16.x
  - run: npm run migrate:latest before tests/deployment

# Performance Monitoring
performance:
  - tool: winston
  - metrics: log endpoint latency via Express middleware (/server/middleware/logLatency.ts)
  - format: JSON { endpoint, method, latency_ms, timestamp }
  - output: file (logs/performance.log) locally, stdout on VPS
  - verify: ensure page load <2s, scan time <5s
  - future: TODO create /dashboard/performance page with Chart.js

# Observability & Logging
logging:
  - tool: winston
  - levels: info, warn, error
  - format: JSON { level, message, timestamp, requestId }
  - output:
    - local: file (logs/app.log, logs/performance.log)
    - vps: stdout, use Docker json-file logging driver (max 10MB per file, 3 files)
  - requestId: generate via Express middleware
  - future: placeholder for Sentry integration

# API & Integration Guidelines
api:
  - define RESTful endpoints with clear resource-based URLs
  - versioning: use /api/v1/ prefix (e.g., /api/v1/health), increment for breaking changes
  - health: /api/v1/health returns { status: string, uptime: number, database: boolean }
  - documentation: create single /docs/api/openapi.yaml (OpenAPI 3.0), stub for MVP
  - validate payloads with Zod, respond with HTTP codes
  - keep controllers thin; orchestrate from /services
  - queue long-running tasks (e.g., PDF generation)
  - notify users via webhook/email for async tasks
integrations:
  - keycloak: use next-auth with Keycloak provider
  - listmonk: use nodemailer with MailHog locally
  - n8n: use mock APIs locally, real APIs on VPS
  - pdf/csv: use pdfkit and csv-writer for reports

# Error Handling
error-handling:
  - response-shape: { code: string, message: string, details?: any }
  - express: use middleware in /server/middleware/errorMiddleware.ts
  - nextjs: use handleApiError.ts for API routes
  - log: capture errors with winston (level: error)
  - client: display user-friendly messages, hide sensitive details

# AI Behavior
ai:
  - autonomy:
    - resolve ambiguities by analyzing codebase (e.g., read_file, codebase_search)
    - infer context from .cursorrules (e.g., prefer strict typing, avoid any)
    - prioritize solutions compliant with project constraints (e.g., free/open-source tools)
  - verification:
    - run npm run format, npm run lint -- --fix, npm run build, npm run test -- --silent in order
    - validate changes against .cursorrules (e.g., naming, structure, security)
    - report verification results (linter errors, test failures, build status)
  - check BUGS.md before generating code to avoid known issues
  - cross-reference .cursorrules to ensure compliance with bug fixes
  - do not apologize for errors; propose fixes
  - add TODO comments for incomplete code
  - prioritize reusable solutions
  - document key decisions in code comments
  - report verification results (linter, tests, build)

# Version Control
git:
  - use Conventional Commits (feat:, fix:, docs:, chore:)
  - keep commit messages under 60 characters
  - include scope (e.g., feat(auth): add Keycloak SSO)
  - commit all modified files as a single logical unit
  - run tests before committing
  - branches:
    - feature/*: new features
    - hotfix/*: urgent fixes
    - release/*: versioned releases
  - tags: vX.Y.Z for prod (e.g., v1.0.0), vX.Y.Z-rc.N for testing
  - hooks:
    - pre-commit: use Husky, run npm run format && npm run lint -- --fix via lint-staged, configured in .lintstagedrc
    - block: fail if lint errors persist

# Testing
testing:
  - use Jest for unit tests
  - use Playwright for E2E tests
  - ensure >90% test coverage
  - test positive, negative, edge, and security cases
  - fix test failures autonomously before committing
  - document test failures and fixes in BUGS.md
  - cleanup: prefer truncate test tables for speed, fallback to rollback test migrations

# Database
database:
  - use node-postgres (pg) for PostgreSQL
  - define schemas with explicit types
  - use parameterized queries to prevent SQL injection
  - encrypt sensitive fields with pgcrypto
  - index frequently queried fields

# Security
security:
  - sanitize inputs with sanitize-html
  - use helmet for Express HTTP headers
  - enforce rate limiting with express-rate-limit
  - encrypt data with pgcrypto in PostgreSQL
  - dependency: run npm audit, fail CI on high/critical issues
  - cors: restrict to ComplyChecker.com
  - csrf: use csurf middleware for forms
  - csp: set Content-Security-Policy headers
  - audit: check APIs for vulnerabilities

# Exclusions
exclusions:
  - no python, django, flask
  - no react-native, vue, svelte
  - no mongodb, mysql
  - no paid tools unless explicitly approved

# Verification
verification:
  - run in order: npm run format, npm run lint -- --fix, npm run build, npm run test -- --silent
  - validate security (helmet, sanitized inputs, npm audit)
  - validate performance (page load <2s, scan time <5s)
  - ensure GDPR/CCPA compliance in data handling