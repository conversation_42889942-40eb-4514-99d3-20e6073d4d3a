#!/usr/bin/env pwsh
# PowerShell script to fix Docker setup and get services running

Write-Host "🐳 Docker Setup and Troubleshooting Script" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan

# Function to check if Dock<PERSON> is running
function Test-DockerRunning {
    try {
        $result = docker version 2>$null
        if ($LASTEXITCODE -eq 0) {
            return $true
        }
        return $false
    }
    catch {
        return $false
    }
}

# Function to check if Docker Desktop is installed
function Test-DockerInstalled {
    try {
        $dockerPath = Get-Command docker -ErrorAction SilentlyContinue
        return $null -ne $dockerPath
    }
    catch {
        return $false
    }
}

# Function to start Docker Desktop
function Start-DockerDesktop {
    Write-Host "🚀 Attempting to start Docker Desktop..." -ForegroundColor Yellow
    
    # Try to find Docker Desktop executable
    $dockerDesktopPaths = @(
        "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe",
        "${env:ProgramFiles(x86)}\Docker\Docker\Docker Desktop.exe",
        "${env:LOCALAPPDATA}\Programs\Docker\Docker\Docker Desktop.exe"
    )
    
    foreach ($path in $dockerDesktopPaths) {
        if (Test-Path $path) {
            Write-Host "Found Docker Desktop at: $path" -ForegroundColor Green
            Start-Process -FilePath $path -WindowStyle Hidden
            Write-Host "Docker Desktop started. Waiting for it to initialize..." -ForegroundColor Yellow
            
            # Wait for Docker to be ready (up to 2 minutes)
            $timeout = 120
            $elapsed = 0
            while ($elapsed -lt $timeout) {
                Start-Sleep -Seconds 5
                $elapsed += 5
                if (Test-DockerRunning) {
                    Write-Host "✅ Docker is now running!" -ForegroundColor Green
                    return $true
                }
                Write-Host "." -NoNewline -ForegroundColor Yellow
            }
            Write-Host ""
            Write-Host "⚠️ Docker Desktop started but not responding yet. Please wait a moment and try again." -ForegroundColor Yellow
            return $false
        }
    }
    
    Write-Host "❌ Docker Desktop executable not found. Please install Docker Desktop." -ForegroundColor Red
    return $false
}

# Main troubleshooting logic
Write-Host "🔍 Checking Docker status..." -ForegroundColor Blue

if (-not (Test-DockerInstalled)) {
    Write-Host "❌ Docker is not installed." -ForegroundColor Red
    Write-Host "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Docker is installed" -ForegroundColor Green

if (-not (Test-DockerRunning)) {
    Write-Host "⚠️ Docker is not running" -ForegroundColor Yellow
    
    if (-not (Start-DockerDesktop)) {
        Write-Host "❌ Failed to start Docker Desktop automatically" -ForegroundColor Red
        Write-Host "Please start Docker Desktop manually and run this script again" -ForegroundColor Yellow
        exit 1
    }
} else {
    Write-Host "✅ Docker is running" -ForegroundColor Green
}

# Clean up any existing containers
Write-Host "🧹 Cleaning up existing containers..." -ForegroundColor Blue
try {
    docker-compose down --remove-orphans 2>$null
    Write-Host "✅ Cleanup completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Cleanup had some issues, continuing..." -ForegroundColor Yellow
}

# Check Docker Compose configuration
Write-Host "📋 Validating Docker Compose configuration..." -ForegroundColor Blue
try {
    $configTest = docker-compose config 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker Compose configuration is valid" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker Compose configuration has errors:" -ForegroundColor Red
        Write-Host $configTest -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Failed to validate Docker Compose configuration" -ForegroundColor Red
    exit 1
}

# Build and start services
Write-Host "🏗️ Building and starting services..." -ForegroundColor Blue

Write-Host "📦 Building backend image..." -ForegroundColor Yellow
try {
    docker-compose build backend
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Backend build failed" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Backend built successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend build failed with exception" -ForegroundColor Red
    exit 1
}

Write-Host "🚀 Starting services..." -ForegroundColor Yellow
try {
    docker-compose up -d
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to start services" -ForegroundColor Red
        Write-Host "Checking logs..." -ForegroundColor Yellow
        docker-compose logs
        exit 1
    }
    Write-Host "✅ Services started successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start services with exception" -ForegroundColor Red
    exit 1
}

# Wait a moment for services to initialize
Write-Host "⏳ Waiting for services to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check service status
Write-Host "📊 Checking service status..." -ForegroundColor Blue
$services = docker-compose ps --format table
Write-Host $services

# Test service endpoints
Write-Host "🌐 Testing service endpoints..." -ForegroundColor Blue

$endpoints = @(
    @{ Name = "Backend Health"; Url = "http://localhost:3001/health"; Expected = 200 },
    @{ Name = "Keycloak"; Url = "http://localhost:8080/auth/health/ready"; Expected = 200 },
    @{ Name = "MailHog"; Url = "http://localhost:8025"; Expected = 200 }
)

foreach ($endpoint in $endpoints) {
    try {
        $response = Invoke-WebRequest -Uri $endpoint.Url -TimeoutSec 10 -UseBasicParsing -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq $endpoint.Expected) {
            Write-Host "✅ $($endpoint.Name) is responding" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $($endpoint.Name) returned status $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $($endpoint.Name) is not responding" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎉 Docker setup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Service URLs:" -ForegroundColor Cyan
Write-Host "  Backend API:     http://localhost:3001" -ForegroundColor White
Write-Host "  Keycloak:        http://localhost:8080/auth" -ForegroundColor White
Write-Host "  MailHog:         http://localhost:8025" -ForegroundColor White
Write-Host "  PostgreSQL:      localhost:5432" -ForegroundColor White
Write-Host ""
Write-Host "💡 Next steps:" -ForegroundColor Yellow
Write-Host "  1. Start the frontend: cd frontend && npm run dev" -ForegroundColor White
Write-Host "  2. Check logs: docker-compose logs -f" -ForegroundColor White
Write-Host "  3. Stop services: docker-compose down" -ForegroundColor White
