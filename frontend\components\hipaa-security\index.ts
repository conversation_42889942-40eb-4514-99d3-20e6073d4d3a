// HIPAA Security Components - Parts 6 & 7 Implementation
export { HipaaSecurityResultsPage } from './HipaaSecurityResultsPage';
export { TestResultsList } from './TestResultsList';
export { FailureEvidenceDisplay } from './FailureEvidenceDisplay';
export { ExecutiveSummary } from './ExecutiveSummary';
export { CategoryBreakdown } from './CategoryBreakdown';
export { VulnerabilityList } from './VulnerabilityList';

// Re-export types for convenience
export type {
  HipaaSecurityScanResult,
  HipaaTestDetail,
  HipaaTestFailure,
  FailureEvidence,
  CategoryResult,
  VulnerabilityResult,
  RiskLevel,
  HipaaCategory,
  ElementType,
  Severity,
  ScanStatus,
  HipaaSecurityResultsPageProps,
  TestResultCardProps,
  FailureEvidenceProps,
} from '@/types/hipaa-security';
