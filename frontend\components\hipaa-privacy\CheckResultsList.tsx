import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/Collapsible';
import { 
  ChevronDown, 
  ChevronRight, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Info,
  ExternalLink,
  FileText
} from 'lucide-react';
import { 
  HipaaPrivacyCheckResult, 
  HipaaPrivacySeverity,
  HipaaPrivacyCheckCategory,
  HipaaPrivacyFinding
} from '@/types/hipaa-privacy';

interface CheckResultsListProps {
  checks: HipaaPrivacyCheckResult[];
  showFailureDetails?: boolean;
}

export const CheckResultsList: React.FC<CheckResultsListProps> = ({
  checks,
  showFailureDetails = true,
}) => {
  const [expandedChecks, setExpandedChecks] = useState<Set<string>>(new Set());

  const toggleExpanded = (checkId: string) => {
    const newExpanded = new Set(expandedChecks);
    if (newExpanded.has(checkId)) {
      newExpanded.delete(checkId);
    } else {
      newExpanded.add(checkId);
    }
    setExpandedChecks(newExpanded);
  };

  const getSeverityColor = (severity: HipaaPrivacySeverity): string => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500 text-white';
      case 'high':
        return 'bg-orange-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-black';
      case 'low':
        return 'bg-blue-500 text-white';
      case 'info':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getSeverityIcon = (severity: HipaaPrivacySeverity) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'medium':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'low':
        return <Info className="h-4 w-4 text-blue-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-gray-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getCategoryColor = (category: HipaaPrivacyCheckCategory): string => {
    switch (category) {
      case HipaaPrivacyCheckCategory.PRESENCE:
        return 'bg-purple-100 text-purple-800';
      case HipaaPrivacyCheckCategory.ACCESSIBILITY:
        return 'bg-blue-100 text-blue-800';
      case HipaaPrivacyCheckCategory.CONTENT_STRUCTURE:
        return 'bg-green-100 text-green-800';
      case HipaaPrivacyCheckCategory.HIPAA_SPECIFIC:
        return 'bg-red-100 text-red-800';
      case HipaaPrivacyCheckCategory.QUALITY:
        return 'bg-yellow-100 text-yellow-800';
      case HipaaPrivacyCheckCategory.LEGAL_COMPLIANCE:
        return 'bg-orange-100 text-orange-800';
      case HipaaPrivacyCheckCategory.CONTACT_INFO:
        return 'bg-teal-100 text-teal-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderFinding = (finding: HipaaPrivacyFinding, index: number) => (
    <div key={index} className="border-l-4 border-gray-200 pl-4 py-2">
      <div className="flex items-start gap-2">
        {getSeverityIcon(finding.severity)}
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-900">{finding.message}</p>
          {finding.location && (
            <p className="text-xs text-gray-500 mt-1">
              <span className="font-medium">Location:</span> {finding.location}
            </p>
          )}
          {finding.context && (
            <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
              <span className="font-medium">Context:</span>
              <pre className="mt-1 whitespace-pre-wrap">{finding.context}</pre>
            </div>
          )}
          {finding.recommendation && (
            <div className="mt-2 p-2 bg-blue-50 rounded text-xs">
              <span className="font-medium text-blue-800">Recommendation:</span>
              <p className="mt-1 text-blue-700">{finding.recommendation}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  if (checks.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-lg font-medium">No checks to display</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {checks.map((check) => {
        const isExpanded = expandedChecks.has(check.checkId);
        
        return (
          <Card key={check.checkId} className="overflow-hidden">
            <Collapsible open={isExpanded} onOpenChange={() => toggleExpanded(check.checkId)}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {check.passed ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                      <div>
                        <CardTitle className="text-lg">{check.checkName}</CardTitle>
                        <p className="text-sm text-gray-600 mt-1">{check.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getSeverityColor(check.severity)}>
                        {check.severity.toUpperCase()}
                      </Badge>
                      <Badge variant="outline" className={getCategoryColor(check.category)}>
                        {check.category.replace('_', ' ').toUpperCase()}
                      </Badge>
                      <div className="text-right">
                        <div className="text-lg font-bold">
                          {check.score}%
                        </div>
                        <div className="text-xs text-gray-500">Score</div>
                      </div>
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4 text-gray-500" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-500" />
                      )}
                    </div>
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              
              <CollapsibleContent>
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    {/* HIPAA Reference */}
                    {check.hipaaReference && (
                      <div className="flex items-center gap-2 p-3 bg-blue-50 rounded">
                        <ExternalLink className="h-4 w-4 text-blue-500" />
                        <span className="text-sm font-medium text-blue-800">
                          HIPAA Reference: {check.hipaaReference}
                        </span>
                      </div>
                    )}

                    {/* Findings */}
                    {showFailureDetails && check.findings && check.findings.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-gray-900 mb-3">
                          Findings ({check.findings.length})
                        </h4>
                        <div className="space-y-3">
                          {check.findings.map((finding, index) => renderFinding(finding, index))}
                        </div>
                      </div>
                    )}

                    {/* Recommendations */}
                    {check.recommendations && check.recommendations.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-gray-900 mb-3">
                          Recommendations
                        </h4>
                        <ul className="space-y-2">
                          {check.recommendations.map((recommendation, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                              <span className="text-sm text-gray-700">{recommendation}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Evidence */}
                    {check.evidence && check.evidence.length > 0 && (
                      <div>
                        <h4 className="text-sm font-semibold text-gray-900 mb-3">
                          Evidence ({check.evidence.length})
                        </h4>
                        <div className="space-y-2">
                          {check.evidence.map((evidence, index) => (
                            <div key={index} className="p-3 bg-gray-50 rounded text-sm">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge variant="outline" className="text-xs">
                                  {evidence.type.toUpperCase()}
                                </Badge>
                                <span className="text-xs text-gray-500">{evidence.location}</span>
                                {evidence.confidence && (
                                  <span className="text-xs text-gray-500">
                                    Confidence: {Math.round(evidence.confidence * 100)}%
                                  </span>
                                )}
                              </div>
                              <pre className="whitespace-pre-wrap text-xs text-gray-700">
                                {evidence.content}
                              </pre>
                              {evidence.context && (
                                <div className="mt-2 pt-2 border-t border-gray-200">
                                  <span className="text-xs font-medium text-gray-600">Context:</span>
                                  <pre className="whitespace-pre-wrap text-xs text-gray-600 mt-1">
                                    {evidence.context}
                                  </pre>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        );
      })}
    </div>
  );
};
