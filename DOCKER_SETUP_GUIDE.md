# 🐳 Docker Setup Guide - Manual Steps

This guide will help you manually set up and run the Docker services for the Comply Checker project.

## Prerequisites

1. **Docker Desktop** must be installed and running
   - Download from: https://www.docker.com/products/docker-desktop
   - Install and restart your computer
   - Start Docker Desktop and wait for it to be ready

## Step-by-Step Setup

### 1. Verify Docker Installation

Open Command Prompt or PowerShell and run:

```bash
docker --version
docker-compose --version
```

You should see version information for both commands.

### 2. Check Docker is Running

```bash
docker ps
```

This should show a list of running containers (may be empty, but shouldn't show an error).

### 3. Navigate to Project Directory

```bash
cd "D:\Web projects\Comply Checker"
```

### 4. Clean Up Previous Containers

```bash
docker-compose down --remove-orphans
```

### 5. Validate Configuration

```bash
docker-compose config
```

This should show the parsed configuration without errors.

### 6. Build the Backend Image

```bash
docker-compose build backend
```

This will:
- Build the Node.js backend
- Install Nuclei for security scanning
- Download Nuclei templates
- Prepare the application

### 7. Start All Services

```bash
docker-compose up -d
```

This starts:
- PostgreSQL database (port 5432)
- Keycloak authentication (port 8080)
- MailHog email testing (port 8025)
- Backend API (port 3001)

### 8. Check Service Status

```bash
docker-compose ps
```

All services should show "Up" status.

### 9. Test Service Endpoints

Open these URLs in your browser:

- **Backend Health**: http://localhost:3001/health
- **Keycloak**: http://localhost:8080/auth
- **MailHog**: http://localhost:8025

### 10. Start Frontend (Separate Terminal)

```bash
cd frontend
npm run dev
```

The frontend will be available at: http://localhost:3000

## Troubleshooting

### Docker Desktop Not Starting

1. Restart Docker Desktop
2. Check Windows Services - Docker Desktop Service should be running
3. Try running as Administrator
4. Restart your computer

### Build Failures

If the backend build fails:

```bash
# Clean Docker cache
docker system prune -a

# Rebuild without cache
docker-compose build --no-cache backend
```

### Port Conflicts

If you get port conflict errors:

```bash
# Check what's using the ports
netstat -an | findstr "3001 5432 8080 8025"

# Stop conflicting services or change ports in docker-compose.yml
```

### Service Won't Start

Check logs for specific service:

```bash
# View all logs
docker-compose logs

# View specific service logs
docker-compose logs backend
docker-compose logs postgres
docker-compose logs keycloak
```

### Nuclei Installation Issues

If Nuclei installation fails in Docker:

1. The Dockerfile will automatically install Nuclei
2. If it fails, check the build logs:
   ```bash
   docker-compose build backend --no-cache
   ```

## Environment Variables

The project uses these key environment variables (in `.env`):

```env
# Database
POSTGRES_DB=complychecker_dev
POSTGRES_USER=complyuser
POSTGRES_PASSWORD=complypassword

# Nuclei Configuration
NUCLEI_PATH=nuclei
NUCLEI_TEMPLATES_PATH=/app/nuclei-templates
NUCLEI_ENABLED=true

# HIPAA Security
HIPAA_SECURITY_ENABLED=true
HIPAA_MAX_PAGES=15
HIPAA_SCAN_TIMEOUT=1800000
```

## Service Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │
│   (Port 3000)   │────│   (Port 3001)   │
└─────────────────┘    └─────────────────┘
                              │
                              ├── PostgreSQL (Port 5432)
                              ├── Keycloak (Port 8080)
                              ├── MailHog (Port 8025)
                              └── Nuclei (Built-in)
```

## Common Commands

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart a service
docker-compose restart backend

# View logs
docker-compose logs -f

# Rebuild and restart
docker-compose down
docker-compose build
docker-compose up -d

# Clean everything
docker-compose down --volumes --remove-orphans
docker system prune -a
```

## Success Indicators

When everything is working correctly:

1. ✅ `docker-compose ps` shows all services as "Up"
2. ✅ http://localhost:3001/health returns JSON response
3. ✅ http://localhost:8080/auth loads Keycloak
4. ✅ http://localhost:8025 loads MailHog
5. ✅ Frontend at http://localhost:3000 can connect to backend

## Next Steps

Once Docker services are running:

1. Start the frontend: `cd frontend && npm run dev`
2. Access the application at http://localhost:3000
3. Test HIPAA security scanning functionality
4. Check that Nuclei is working (no ZAP dependencies)

## Getting Help

If you continue to have issues:

1. Run the diagnostic script: `powershell -ExecutionPolicy Bypass -File ".\scripts\docker-diagnostic.ps1"`
2. Check Docker Desktop logs
3. Ensure Windows virtualization is enabled
4. Try running Docker Desktop as Administrator
