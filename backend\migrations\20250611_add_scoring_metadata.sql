-- Migration: Add scoring metadata and improved scoring support
-- Date: 2025-06-11
-- Purpose: Support improved scoring system with proper weighting and transparency

-- Add scoring metadata table
CREATE TABLE IF NOT EXISTS hipaa_scoring_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    hipaa_scan_id UUID NOT NULL REFERENCES hipaa_scans(id) ON DELETE CASCADE,
    
    -- Scoring version and method
    scoring_version VARCHAR(10) NOT NULL DEFAULT '2.0',
    scoring_method VARCHAR(50) NOT NULL DEFAULT 'weighted_checks',
    
    -- Industry context
    industry_type VARCHAR(20) NOT NULL DEFAULT 'general',
    thresholds_used JSONB NOT NULL DEFAULT '{}',
    
    -- Detailed scoring breakdown
    check_weights JSONB NOT NULL DEFAULT '{}',
    check_contributions JSONB NOT NULL DEFAULT '{}',
    level_weights JSONB NOT NULL DEFAULT '{}',
    
    -- Calculation details
    raw_weighted_score DECIMAL(5,2) NOT NULL,
    confidence_adjusted BOOLEAN NOT NULL DEFAULT false,
    confidence_weight_factor DECIMAL(3,2) DEFAULT NULL,
    
    -- Comparison with old scoring
    legacy_score DECIMAL(5,2) DEFAULT NULL,
    score_difference DECIMAL(5,2) DEFAULT NULL,
    migration_notes TEXT DEFAULT NULL,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance
CREATE INDEX idx_hipaa_scoring_metadata_scan_id ON hipaa_scoring_metadata(hipaa_scan_id);
CREATE INDEX idx_hipaa_scoring_metadata_version ON hipaa_scoring_metadata(scoring_version);
CREATE INDEX idx_hipaa_scoring_metadata_industry ON hipaa_scoring_metadata(industry_type);

-- Add scoring version to main hipaa_scans table
ALTER TABLE hipaa_scans 
ADD COLUMN IF NOT EXISTS scoring_version VARCHAR(10) DEFAULT '1.0',
ADD COLUMN IF NOT EXISTS industry_type VARCHAR(20) DEFAULT 'general',
ADD COLUMN IF NOT EXISTS raw_weighted_score DECIMAL(5,2) DEFAULT NULL;

-- Add check weight tracking to hipaa_check_results
ALTER TABLE hipaa_check_results
ADD COLUMN IF NOT EXISTS check_weight DECIMAL(3,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS score_contribution DECIMAL(5,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS confidence_adjusted_score DECIMAL(5,2) DEFAULT NULL;

-- Add level scoring transparency to hipaa_level_results  
ALTER TABLE hipaa_level_results
ADD COLUMN IF NOT EXISTS weight_used DECIMAL(3,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS contribution_to_check DECIMAL(5,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS calculation_notes TEXT DEFAULT NULL;

-- Create view for scoring analysis
CREATE OR REPLACE VIEW hipaa_scoring_analysis AS
SELECT 
    hs.id as scan_id,
    hs.target_url,
    hs.overall_score as final_score,
    hs.scoring_version,
    hs.industry_type,
    hs.raw_weighted_score,
    
    -- Check breakdown
    json_agg(
        json_build_object(
            'checkId', hcr.check_id,
            'name', hcr.name,
            'score', hcr.overall_score,
            'weight', hcr.check_weight,
            'contribution', hcr.score_contribution,
            'passed', hcr.passed,
            'confidence', hcr.confidence
        ) ORDER BY hcr.check_weight DESC
    ) as check_breakdown,
    
    -- Scoring metadata
    hsm.check_weights,
    hsm.check_contributions,
    hsm.legacy_score,
    hsm.score_difference,
    
    hs.created_at
FROM hipaa_scans hs
LEFT JOIN hipaa_check_results hcr ON hs.id = hcr.hipaa_scan_id
LEFT JOIN hipaa_scoring_metadata hsm ON hs.id = hsm.hipaa_scan_id
GROUP BY hs.id, hsm.id;

-- Create function to update scoring metadata
CREATE OR REPLACE FUNCTION update_hipaa_scoring_metadata()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER trigger_update_hipaa_scoring_metadata
    BEFORE UPDATE ON hipaa_scoring_metadata
    FOR EACH ROW
    EXECUTE FUNCTION update_hipaa_scoring_metadata();

-- Add comments for documentation
COMMENT ON TABLE hipaa_scoring_metadata IS 'Stores detailed scoring calculation metadata for transparency and debugging';
COMMENT ON COLUMN hipaa_scoring_metadata.scoring_version IS 'Version of scoring algorithm used (1.0=legacy, 2.0=improved)';
COMMENT ON COLUMN hipaa_scoring_metadata.industry_type IS 'Industry context for threshold selection (healthcare/general)';
COMMENT ON COLUMN hipaa_scoring_metadata.check_weights IS 'JSON object containing weights used for each check';
COMMENT ON COLUMN hipaa_scoring_metadata.check_contributions IS 'JSON object containing score contributions from each check';
COMMENT ON COLUMN hipaa_scoring_metadata.raw_weighted_score IS 'Unrounded weighted score before final processing';
COMMENT ON COLUMN hipaa_scoring_metadata.confidence_adjusted IS 'Whether confidence weighting was applied';
COMMENT ON COLUMN hipaa_scoring_metadata.legacy_score IS 'Score from old algorithm for comparison';
COMMENT ON COLUMN hipaa_scoring_metadata.score_difference IS 'Difference between new and legacy scores';

COMMENT ON VIEW hipaa_scoring_analysis IS 'Comprehensive view for analyzing scoring calculations and trends';
