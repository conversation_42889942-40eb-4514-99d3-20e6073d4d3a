# 🐳 Docker Desktop Startup & Troubleshooting Guide

## Step 1: Check if Docker Desktop is Installed

### Method 1: Start Menu
1. Press `Windows Key`
2. Type "Docker Desktop"
3. If you see "Docker Desktop" in the results, it's installed
4. Click on it to start

### Method 2: File Explorer
Check these locations for `Docker Desktop.exe`:
- `C:\Program Files\Docker\Docker\Docker Desktop.exe`
- `C:\Program Files (x86)\Docker\Docker\Docker Desktop.exe`
- `%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe`

### If Not Installed:
1. Download from: https://www.docker.com/products/docker-desktop
2. Install and restart your computer
3. Continue with Step 2

## Step 2: Start Docker Desktop

### Manual Start:
1. **Double-click** on Docker Desktop icon (Desktop or Start Menu)
2. **Wait** for the application to load (may take 30-60 seconds)
3. **Look for** the Docker whale icon in your system tray (bottom-right corner)

### System Tray Icon Status:
- 🔄 **Animating whale**: Docker is starting up (wait)
- 🐳 **Steady whale**: Docker is running
- ❌ **Red X or error**: Docker has issues

## Step 3: Wait for Docker to Initialize

### First-Time Startup:
- Can take **2-5 minutes**
- Docker needs to download and set up components
- **Don't close** Docker Desktop during this process

### Subsequent Startups:
- Usually takes **30-60 seconds**
- Faster after first setup

## Step 4: Verify Docker is Running

### Method 1: System Tray
1. Right-click the Docker whale icon
2. Should show "Docker Desktop is running"
3. If it shows errors, see troubleshooting below

### Method 2: Command Line
1. Open **Command Prompt** or **PowerShell**
2. Run: `docker --version`
3. Should show Docker version (e.g., "Docker version 24.0.7")
4. Run: `docker ps`
5. Should show empty table (no errors)

## Step 5: Test Docker Functionality

Run these commands in Command Prompt:

```cmd
# Check Docker version
docker --version

# Check Docker Compose
docker-compose --version

# Test Docker daemon
docker ps

# Test with a simple container
docker run hello-world
```

All commands should work without errors.

## 🔧 Troubleshooting Common Issues

### Issue 1: Docker Desktop Won't Start

**Symptoms:**
- Double-clicking does nothing
- Error messages on startup
- Crashes immediately

**Solutions:**
1. **Run as Administrator:**
   - Right-click Docker Desktop
   - Select "Run as administrator"

2. **Restart Computer:**
   - Sometimes Windows needs a restart
   - Especially after fresh installation

3. **Check Windows Version:**
   - Docker requires Windows 10/11
   - Home edition needs WSL2
   - Pro/Enterprise can use Hyper-V

### Issue 2: "Docker daemon is not running"

**Symptoms:**
- `docker ps` shows connection errors
- Commands timeout

**Solutions:**
1. **Wait Longer:**
   - First startup can take 5+ minutes
   - Check system tray icon

2. **Restart Docker Desktop:**
   - Right-click system tray icon
   - Select "Restart Docker Desktop"

3. **Check Windows Services:**
   - Press `Win + R`, type `services.msc`
   - Look for "Docker Desktop Service"
   - Should be "Running"

### Issue 3: WSL2 Issues (Windows Home)

**Symptoms:**
- "WSL2 installation is incomplete"
- "WSL2 kernel update required"

**Solutions:**
1. **Install WSL2:**
   ```cmd
   wsl --install
   ```

2. **Update WSL2 Kernel:**
   - Download from: https://aka.ms/wsl2kernel
   - Install and restart

3. **Enable WSL2:**
   ```cmd
   wsl --set-default-version 2
   ```

### Issue 4: Hyper-V Issues (Windows Pro/Enterprise)

**Symptoms:**
- "Hyper-V is not enabled"
- Virtualization errors

**Solutions:**
1. **Enable Hyper-V:**
   - Control Panel → Programs → Windows Features
   - Check "Hyper-V"
   - Restart computer

2. **Check BIOS:**
   - Restart computer
   - Enter BIOS/UEFI settings
   - Enable "Virtualization Technology"
   - Enable "VT-d" (if available)

### Issue 5: Port Conflicts

**Symptoms:**
- "Port already in use" errors
- Services won't start

**Solutions:**
1. **Check Running Services:**
   ```cmd
   netstat -an | findstr "5432 8080 3001"
   ```

2. **Stop Conflicting Services:**
   - Stop any local PostgreSQL (port 5432)
   - Stop any local web servers (port 8080, 3001)

## 🚀 Once Docker Desktop is Running

### Test Our Project:
1. **Open Command Prompt** in project directory:
   ```cmd
   cd "D:\Web projects\Comply Checker"
   ```

2. **Clean up any old containers:**
   ```cmd
   docker-compose down --remove-orphans
   ```

3. **Build and start services:**
   ```cmd
   docker-compose build backend
   docker-compose up -d
   ```

4. **Check status:**
   ```cmd
   docker-compose ps
   ```

5. **Test endpoints:**
   - Backend: http://localhost:3001/health
   - Keycloak: http://localhost:8080/auth
   - MailHog: http://localhost:8025

## 📊 Expected Results

When everything is working:

```cmd
C:\> docker --version
Docker version 24.0.7, build afdd53b

C:\> docker ps
CONTAINER ID   IMAGE     COMMAND   CREATED   STATUS    PORTS     NAMES

C:\> docker-compose ps
NAME                        COMMAND                  SERVICE    STATUS
comply_checker_backend      "npm run start"          backend    running
comply_checker_keycloak     "start-dev"              keycloak   running
comply_checker_mailhog      "MailHog"                mailhog    running
comply_checker_postgres     "postgres -c password_…" postgres   running
```

## 🆘 If Nothing Works

### Last Resort Options:

1. **Reinstall Docker Desktop:**
   - Uninstall completely
   - Restart computer
   - Download fresh installer
   - Install as Administrator

2. **Check System Requirements:**
   - Windows 10 version 2004+ or Windows 11
   - 4GB RAM minimum
   - Virtualization enabled in BIOS

3. **Alternative: Use Docker Toolbox:**
   - For older systems
   - Download from Docker's legacy releases

4. **Check Windows Event Viewer:**
   - Press `Win + R`, type `eventvwr.msc`
   - Look under "Windows Logs" → "Application"
   - Search for Docker-related errors

## 📞 Getting Help

If you're still having issues:

1. **Docker Desktop Logs:**
   - Right-click Docker whale icon
   - Select "Troubleshoot"
   - Click "Get support"

2. **System Information:**
   - Run `systeminfo` in Command Prompt
   - Check Windows version and features

3. **Hardware Check:**
   - Ensure virtualization is enabled
   - Check available RAM and disk space

Remember: Docker Desktop startup issues are common, especially on first installation. Most issues are resolved by restarting the computer and running as Administrator.
