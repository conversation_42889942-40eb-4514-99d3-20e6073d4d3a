@echo off
echo 🔍 Docker Verification and Service Startup
echo ==========================================

echo Step 1: Verifying Docker is running...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker command not found
    echo Please ensure Docker Desktop is installed and running
    pause
    exit /b 1
)

echo ✅ Docker CLI is available

docker ps >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker daemon is not running
    echo Please start Docker Desktop and wait for it to be ready
    echo Look for the Docker whale icon in your system tray
    pause
    exit /b 1
)

echo ✅ Docker daemon is running

echo Step 2: Checking Docker Compose...
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose not available
    pause
    exit /b 1
)

echo ✅ Docker Compose is available

echo Step 3: Checking project directory...
if not exist "docker-compose.yml" (
    echo ❌ docker-compose.yml not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

echo ✅ docker-compose.yml found

echo Step 4: Cleaning up any existing containers...
docker-compose down --remove-orphans >nul 2>&1
echo ✅ Cleanup completed

echo Step 5: Validating Docker Compose configuration...
docker-compose config >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose configuration has errors
    echo Running config check with output:
    docker-compose config
    pause
    exit /b 1
)

echo ✅ Docker Compose configuration is valid

echo Step 6: Building backend image (this may take a few minutes)...
echo This will install Node.js dependencies and Nuclei security scanner
docker-compose build backend
if %errorlevel% neq 0 (
    echo ❌ Backend build failed
    echo Check the error messages above
    pause
    exit /b 1
)

echo ✅ Backend image built successfully

echo Step 7: Starting all services...
docker-compose up -d
if %errorlevel% neq 0 (
    echo ❌ Failed to start services
    echo Checking logs:
    docker-compose logs
    pause
    exit /b 1
)

echo ✅ Services started successfully

echo Step 8: Waiting for services to initialize...
timeout /t 15 /nobreak >nul

echo Step 9: Checking service status...
docker-compose ps

echo Step 10: Testing service endpoints...
echo Testing Backend Health Check...
curl -s http://localhost:3001/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend is responding
) else (
    echo ⚠️ Backend not responding yet (may still be starting)
)

echo Testing Keycloak...
curl -s http://localhost:8080/auth/health/ready >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Keycloak is responding
) else (
    echo ⚠️ Keycloak not responding yet (may still be starting)
)

echo Testing MailHog...
curl -s http://localhost:8025 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MailHog is responding
) else (
    echo ⚠️ MailHog not responding yet (may still be starting)
)

echo.
echo 🎉 Docker services setup completed!
echo.
echo 📋 Service URLs:
echo   Backend API:     http://localhost:3001
echo   Backend Health:  http://localhost:3001/health
echo   Keycloak:        http://localhost:8080/auth
echo   MailHog:         http://localhost:8025
echo   PostgreSQL:      localhost:5432
echo.
echo 📊 Service Status:
docker-compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
echo.
echo 💡 Next Steps:
echo 1. Start the frontend:
echo    cd frontend
echo    npm run dev
echo.
echo 2. Access the application at: http://localhost:3000
echo.
echo 3. Check logs if needed:
echo    docker-compose logs -f
echo.
echo 4. Stop services when done:
echo    docker-compose down
echo.
echo 🔧 If services are not responding:
echo • Wait a few more minutes (first startup takes longer)
echo • Check logs: docker-compose logs [service-name]
echo • Restart a service: docker-compose restart [service-name]
echo.
pause
