# 🔧 Docker Desktop "com.docker.build: exit status 1" Error Fix

## Error Description
```
An unexpected error occurred: running services: running services: running com.docker.build: com.docker.build: running com.docker.build: exit status 1
```

This error typically occurs when Docker Desktop's internal services fail to start, often due to corrupted data or configuration conflicts.

## 🚀 Quick Fix (Try This First)

### Method 1: Reset Docker Desktop Data
1. **Close Docker Desktop completely**
2. **Run the fix script as Administrator:**
   ```cmd
   cd "D:\Web projects\Comply Checker"
   .\scripts\fix-docker-desktop-error.bat
   ```
3. **Wait for the script to complete**
4. **Docker Desktop should start automatically**

### Method 2: Manual Reset
1. **Stop Docker Desktop:**
   - Right-click Docker icon in system tray
   - Select "Quit Docker Desktop"
   - Wait 30 seconds

2. **Clear Docker data:**
   - Press `Win + R`, type `%APPDATA%` and press Enter
   - Delete the `Docker` folder if it exists
   - Press `Win + R`, type `%LOCALAPPDATA%` and press Enter  
   - Delete the `Docker` folder if it exists
   - Press `Win + R`, type `%PROGRAMDATA%` and press Enter
   - Delete the `Docker` and `DockerDesktop` folders if they exist

3. **Restart Docker Desktop as Administrator**

## 🔍 Advanced Troubleshooting

### Step 1: Complete Process Cleanup
Open **Command Prompt as Administrator** and run:
```cmd
taskkill /F /IM "Docker Desktop.exe"
taskkill /F /IM "com.docker.cli.exe"
taskkill /F /IM "com.docker.backend.exe"
taskkill /F /IM "com.docker.proxy.exe"
taskkill /F /IM "dockerd.exe"
net stop "Docker Desktop Service"
```

### Step 2: Registry Cleanup (Advanced)
⚠️ **Warning: Only do this if you're comfortable editing the registry**

1. Press `Win + R`, type `regedit`, press Enter
2. Navigate to: `HKEY_CURRENT_USER\Software\Docker Inc.`
3. Delete the `Docker Desktop` key if it exists
4. Navigate to: `HKEY_LOCAL_MACHINE\SOFTWARE\Docker Inc.`
5. Delete the `Docker Desktop` key if it exists

### Step 3: Windows Features Check
1. Press `Win + R`, type `appwiz.cpl`, press Enter
2. Click "Turn Windows features on or off"
3. Ensure these are enabled:
   - **Containers** ✅
   - **Hyper-V** ✅ (Windows Pro/Enterprise)
   - **Windows Subsystem for Linux** ✅ (Windows Home)
4. Restart computer if changes were made

### Step 4: Network Reset
Open **Command Prompt as Administrator**:
```cmd
netsh winsock reset
netsh int ip reset
ipconfig /flushdns
```
Restart computer after this.

## 🛠️ Alternative Solutions

### Solution 1: Switch Docker Backend
If Docker Desktop starts but still has issues:
1. Open Docker Desktop Settings
2. Go to **General** tab
3. Try switching between:
   - "Use WSL 2 based engine" (for Windows Home)
   - "Use Hyper-V backend" (for Windows Pro/Enterprise)
4. Apply & Restart

### Solution 2: Reinstall Docker Desktop
1. **Uninstall Docker Desktop:**
   - Control Panel → Programs → Uninstall Docker Desktop
   - OR use the uninstaller in Docker installation folder

2. **Clean remaining files:**
   - Delete `%PROGRAMDATA%\Docker`
   - Delete `%PROGRAMDATA%\DockerDesktop`
   - Delete `%APPDATA%\Docker`
   - Delete `%LOCALAPPDATA%\Docker`

3. **Restart computer**

4. **Download fresh installer:**
   - Go to: https://www.docker.com/products/docker-desktop
   - Download for Windows
   - Install as Administrator

### Solution 3: WSL2 Setup (Windows Home)
If you're on Windows Home edition:

1. **Install WSL2:**
   ```cmd
   wsl --install
   ```

2. **Update WSL2 kernel:**
   - Download: https://aka.ms/wsl2kernel
   - Install the update

3. **Set WSL2 as default:**
   ```cmd
   wsl --set-default-version 2
   ```

4. **Restart computer and try Docker Desktop again**

### Solution 4: Hyper-V Setup (Windows Pro/Enterprise)
If you're on Windows Pro/Enterprise:

1. **Enable Hyper-V:**
   - Control Panel → Programs → Windows Features
   - Check "Hyper-V" (all sub-items)
   - Restart computer

2. **Check BIOS settings:**
   - Restart computer
   - Enter BIOS/UEFI (usually F2, F12, or Del during boot)
   - Enable "Intel VT-x" or "AMD-V"
   - Enable "VT-d" if available
   - Save and exit

## 🔍 Diagnostic Commands

Run these to check your system:

```cmd
# Check Windows version
winver

# Check Docker installation
where docker

# Check running processes
tasklist | findstr docker

# Check Windows features
dism /online /get-features | findstr -i hyper
dism /online /get-features | findstr -i container

# Check WSL2
wsl --list --verbose

# Check virtualization
systeminfo | findstr /i "hyper"
```

## 🎯 Expected Results After Fix

When Docker Desktop starts successfully:

1. **System tray icon:** Steady Docker whale (not animating)
2. **Right-click menu:** Shows "Docker Desktop is running"
3. **Command line test:**
   ```cmd
   docker --version
   docker ps
   ```
   Both should work without errors

## 🆘 If Nothing Works

### Last Resort Options:

1. **Windows Update:**
   - Update to latest Windows 10 (version 2004+) or Windows 11
   - Install all pending updates

2. **Hardware Check:**
   - Ensure virtualization is enabled in BIOS
   - Check available RAM (minimum 4GB)
   - Check disk space (minimum 20GB free)

3. **Alternative Docker Solutions:**
   - Docker Toolbox (for older systems)
   - Podman Desktop (Docker alternative)
   - Use Linux VM with Docker

4. **System Restore:**
   - If Docker was working before, restore to previous point
   - Control Panel → Recovery → Open System Restore

## 📞 Getting Additional Help

If you're still stuck:

1. **Docker Desktop Logs:**
   - `%LOCALAPPDATA%\Docker\log.txt`
   - Look for specific error messages

2. **Windows Event Viewer:**
   - Press `Win + R`, type `eventvwr.msc`
   - Check "Windows Logs" → "Application"
   - Look for Docker-related errors

3. **Docker Community:**
   - Docker Desktop GitHub issues
   - Docker Community Forums
   - Stack Overflow with "docker-desktop" tag

## ✅ Success Verification

Once Docker Desktop is working, verify with our project:

```cmd
cd "D:\Web projects\Comply Checker"
docker-compose config
docker-compose build backend
docker-compose up -d
docker-compose ps
```

All commands should complete successfully, and you should see 4 running containers (backend, postgres, keycloak, mailhog) with no ZAP container.
