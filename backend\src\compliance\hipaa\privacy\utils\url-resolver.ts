// backend/src/compliance/hipaa/privacy/utils/url-resolver.ts

import axios, { AxiosInstance } from 'axios';
import * as https from 'https';
import { JSDOM } from 'jsdom';
import { PrivacyPolicyLink } from '../types';
import { PERFORMANCE_CONFIG, ERROR_CODES } from '../constants';

/**
 * Validated privacy policy link with accessibility information
 */
export interface ValidatedLink extends PrivacyPolicyLink {
  validated: boolean;
  contentLength?: number;
  error?: string;
  responseTime?: number;
  statusCode?: number;
}

/**
 * URL resolution utilities for finding and validating privacy policy pages
 * Handles the discovery phase of the 3-level analysis workflow
 */
export class URLResolver {
  private static httpClient: AxiosInstance;

  /**
   * Initialize the secure HTTP client
   */
  private static initializeHttpClient(): void {
    if (this.httpClient !== undefined) return;

    this.httpClient = axios.create({
      timeout: PERFORMANCE_CONFIG.TIMEOUTS.PAGE_FETCH,
      maxRedirects: 5,
      maxContentLength: 10 * 1024 * 1024, // 10MB limit
      httpsAgent: new https.Agent({
        rejectUnauthorized: true,
        secureProtocol: 'TLSv1_2_method',
      }),
      headers: {
        'User-Agent': 'ComplyChecker-HIPAA/2.0 (+https://complychecker.com/bot)',
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        DNT: '1',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    });

    // Request interceptor for security validation
    this.httpClient.interceptors.request.use((config) => {
      if (config.url && !this.isAllowedUrl(config.url)) {
        throw new Error('URL not allowed for security reasons');
      }
      return config;
    });
  }

  /**
   * Finds privacy policy links on a webpage
   * Returns all potential privacy policy links found
   */
  static findPrivacyPolicyLinks(html: string, baseUrl?: string): PrivacyPolicyLink[] {
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;
      const links = Array.from(document.querySelectorAll('a[href]'));
      const privacyLinks: PrivacyPolicyLink[] = [];

      // Privacy policy link patterns (enhanced to catch more variations)
      const privacyPatterns = [
        /privacy\s*policy/i,
        /privacy\s*notice/i,
        /hipaa\s*notice/i,
        /notice\s*of\s*privacy\s*practices/i,
        /privacy\s*statement/i,
        /data\s*privacy/i,
        /\bprivacy\b/i, // Just "Privacy" link
        /data\s*protection/i,
        /cookie\s*policy/i,
        /terms\s*.*\s*privacy/i,
      ];

      for (const link of links) {
        const href = link.getAttribute('href');
        const text = link.textContent?.trim() || '';

        if (!href || !text) continue;

        // Check if link text matches privacy patterns
        const isPrivacyLink = privacyPatterns.some((pattern) => pattern.test(text));

        // Also check href for privacy indicators
        const hasPrivacyInUrl = privacyPatterns.some((pattern) => pattern.test(href));

        if (isPrivacyLink || hasPrivacyInUrl) {
          const absoluteUrl = this.resolveUrl(href, baseUrl);

          if (absoluteUrl) {
            privacyLinks.push({
              url: absoluteUrl,
              text: text,
              type: this.classifyLinkType(text, href),
              accessible: true, // Will be validated separately
              format: this.detectLinkFormat(href),
            });
          }
        }
      }

      // Remove duplicates based on URL
      const uniqueLinks = privacyLinks.filter(
        (link, index, array) => array.findIndex((l) => l.url === link.url) === index,
      );

      return uniqueLinks;
    } catch (error) {
      console.error('Error finding privacy policy links:', error);
      return [];
    }
  }

  /**
   * Validates that privacy policy pages are accessible and contain real content
   */
  static async validatePolicyPages(links: PrivacyPolicyLink[]): Promise<ValidatedLink[]> {
    this.initializeHttpClient();
    const validatedLinks: ValidatedLink[] = [];

    for (const link of links) {
      const startTime = Date.now();

      try {
        const response = await this.httpClient.get(link.url);
        const responseTime = Date.now() - startTime;

        // Validate that the page contains actual policy content
        const isValidPolicy = this.isValidPrivacyPolicy(response.data);

        validatedLinks.push({
          ...link,
          validated: isValidPolicy,
          contentLength: response.data.length,
          responseTime,
          statusCode: response.status,
        });
      } catch (error) {
        const responseTime = Date.now() - startTime;

        validatedLinks.push({
          ...link,
          validated: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          responseTime,
          statusCode: axios.isAxiosError(error) ? error.response?.status : undefined,
        });
      }
    }

    return validatedLinks;
  }

  /**
   * Downloads and returns the content of privacy policy pages
   */
  static async resolvePrivacyPages(links: ValidatedLink[]): Promise<
    Array<{
      link: ValidatedLink;
      content: string;
      error?: string;
    }>
  > {
    this.initializeHttpClient();
    const results: Array<{ link: ValidatedLink; content: string; error?: string }> = [];

    for (const link of links.filter((l) => l.validated)) {
      try {
        const response = await this.httpClient.get(link.url);

        results.push({
          link,
          content: response.data,
        });
      } catch (error) {
        results.push({
          link,
          content: '',
          error: error instanceof Error ? error.message : 'Failed to fetch content',
        });
      }
    }

    return results;
  }

  /**
   * Validates that a page contains actual privacy policy content
   */
  private static isValidPrivacyPolicy(content: string): boolean {
    if (!content || content.length < 500) {
      return false; // Too short to be a real policy
    }

    // Check for common privacy policy indicators
    const privacyIndicators = [
      /privacy/i,
      /information/i,
      /data/i,
      /collect/i,
      /use/i,
      /share/i,
      /protect/i,
    ];

    const indicatorCount = privacyIndicators.filter((indicator) => indicator.test(content)).length;

    // Should have at least 4 out of 7 indicators
    if (indicatorCount < 4) {
      return false;
    }

    // Check for "coming soon" or placeholder content
    const placeholderPatterns = [
      /coming\s*soon/i,
      /under\s*construction/i,
      /page\s*not\s*found/i,
      /404/i,
      /lorem\s*ipsum/i,
    ];

    const hasPlaceholder = placeholderPatterns.some((pattern) => pattern.test(content));

    return !hasPlaceholder;
  }

  /**
   * Resolves relative URLs to absolute URLs
   */
  static resolveUrl(href: string, baseUrl?: string): string | null {
    try {
      // If href is already absolute, return it
      if (href.startsWith('http://') || href.startsWith('https://')) {
        return href;
      }

      // If no base URL provided, can't resolve relative URLs
      if (!baseUrl) {
        return null;
      }

      // Resolve relative URL
      const base = new URL(baseUrl);
      const resolved = new URL(href, base);

      return resolved.toString();
    } catch (error) {
      console.warn('Failed to resolve URL:', href, error);
      return null;
    }
  }

  /**
   * Classifies the type of privacy link
   */
  private static classifyLinkType(
    text: string,
    href: string,
  ): 'privacy_policy' | 'privacy_notice' | 'hipaa_notice' {
    const lowerText = text.toLowerCase();
    const lowerHref = href.toLowerCase();

    if (
      lowerText.includes('hipaa') ||
      lowerHref.includes('hipaa') ||
      lowerText.includes('notice of privacy practices')
    ) {
      return 'hipaa_notice';
    }

    if (lowerText.includes('notice') || lowerHref.includes('notice')) {
      return 'privacy_notice';
    }

    return 'privacy_policy';
  }

  /**
   * Detects the format of a link
   */
  private static detectLinkFormat(href: string): 'html' | 'pdf' | 'doc' | 'txt' {
    const lowerHref = href.toLowerCase();

    if (lowerHref.endsWith('.pdf') || lowerHref.includes('.pdf?')) {
      return 'pdf';
    }

    if (
      lowerHref.endsWith('.doc') ||
      lowerHref.endsWith('.docx') ||
      lowerHref.includes('.doc?') ||
      lowerHref.includes('.docx?')
    ) {
      return 'doc';
    }

    if (lowerHref.endsWith('.txt') || lowerHref.includes('.txt?')) {
      return 'txt';
    }

    return 'html';
  }

  /**
   * Validates if a URL is allowed for security reasons
   */
  private static isAllowedUrl(url: string): boolean {
    try {
      const parsed = new URL(url);

      // Block private IP ranges
      const hostname = parsed.hostname;
      if (this.isPrivateIP(hostname)) {
        return false;
      }

      // Block localhost and loopback
      if (['localhost', '127.0.0.1', '0.0.0.0', '::1'].includes(hostname)) {
        return false;
      }

      // Only allow HTTP and HTTPS
      if (!['http:', 'https:'].includes(parsed.protocol)) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Checks if hostname is a private IP address
   */
  private static isPrivateIP(hostname: string): boolean {
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^169\.254\./,
      /^fc00:/,
      /^fe80:/,
    ];

    return privateRanges.some((range) => range.test(hostname));
  }

  /**
   * Fetches page content with error handling
   */
  static async fetchPageContent(
    url: string,
    options?: { timeout?: number; userAgent?: string },
  ): Promise<string> {
    this.initializeHttpClient();

    try {
      const config: { timeout?: number; headers?: Record<string, string> } = {};
      if (options?.timeout) {
        config.timeout = options.timeout;
      }
      if (options?.userAgent) {
        config.headers = { 'User-Agent': options.userAgent };
      }

      const response = await this.httpClient.get(url, config);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new Error(`${ERROR_CODES.TIMEOUT}: Request timed out`);
        }
        if (error.response?.status === 404) {
          throw new Error(`${ERROR_CODES.FETCH_FAILED}: Page not found`);
        }
        if (error.response?.status && error.response.status >= 500) {
          throw new Error(`${ERROR_CODES.FETCH_FAILED}: Server error`);
        }
      }

      throw new Error(
        `${ERROR_CODES.FETCH_FAILED}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Find privacy policy URLs from a website
   * @param targetUrl - The main website URL to search
   * @returns Promise<string[]> - Array of privacy policy URLs found
   */
  static async findPrivacyPolicyUrls(targetUrl: string): Promise<string[]> {
    try {
      const htmlContent = await this.fetchPageContent(targetUrl);
      const privacyLinks = this.findPrivacyPolicyLinks(htmlContent, targetUrl);
      return privacyLinks.map((link) => link.url);
    } catch (error) {
      console.warn(`Failed to find privacy policy URLs for ${targetUrl}:`, error);
      return [];
    }
  }
}
