2025-06-18 15:09:18.671 | 2025-06-18 09:39:18,671 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) new JtaTransactionWrapper
2025-06-18 15:09:18.671 | 2025-06-18 09:39:18,671 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) was existing? false
2025-06-18 15:09:18.672 | 2025-06-18 09:39:18,672 DEBUG [io.quarkus.vertx.http.runtime.ForwardedParser] (executor-thread-12) Recalculated absoluteURI to http://localhost:8080/auth/realms/complychecker/protocol/openid-connect/auth?client_id=complychecker-frontend&redirect_uri=http%3A%2F%2Flocalhost%3A3000%2F&state=c5789961-f2eb-4de1-b65a-0746b4a69fb1&response_mode=fragment&response_type=code&scope=openid&nonce=b771df95-dd02-4ae4-a121-c7a53d22cb0d&code_challenge=kvQwDxM230rFRVgGsAJMFgGKzl_Yj9AtTJ4abEYt9tA&code_challenge_method=S256
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,673 DEBUG [org.keycloak.services.managers.AuthenticationManager] (executor-thread-12) Could not find cookie: KEYCLOAK_IDENTITY
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,674 DEBUG [org.keycloak.services.managers.AuthenticationSessionManager] (executor-thread-12) Set AUTH_SESSION_ID cookie with value e279dfd6-bd7e-4b13-b6ad-b9fa215eb9d7
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,674 DEBUG [org.keycloak.protocol.AuthorizationEndpointBase] (executor-thread-12) Sent request to authz endpoint. Created new root authentication session with ID 'e279dfd6-bd7e-4b13-b6ad-b9fa215eb9d7' . Client: complychecker-frontend . New authentication session tab ID: ju8uACaPu7Y
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,675 DEBUG [org.keycloak.authentication.AuthenticationProcessor] (executor-thread-12) AUTHENTICATE
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,675 DEBUG [org.keycloak.authentication.AuthenticationProcessor] (executor-thread-12) AUTHENTICATE ONLY
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,675 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) processFlow: browser
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,675 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) check execution: 'auth-cookie', requirement: 'ALTERNATIVE'
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,675 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) authenticator: auth-cookie
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,675 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-12) Going through the flow 'browser' for adding executions
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,675 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-12) Going through the flow 'forms' for adding executions
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-12) Selections when trying execution 'auth-cookie' : [ authSelection - auth-cookie,  authSelection - identity-provider-redirector,  authSelection - auth-username-password-form]
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) invoke authenticator.authenticate: auth-cookie
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.services.managers.AuthenticationManager] (executor-thread-12) Could not find cookie: KEYCLOAK_IDENTITY
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) authenticator ATTEMPTED: auth-cookie
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) check execution: 'identity-provider-redirector', requirement: 'ALTERNATIVE'
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) authenticator: identity-provider-redirector
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-12) Going through the flow 'browser' for adding executions
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-12) Going through the flow 'forms' for adding executions
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-12) Selections when trying execution 'identity-provider-redirector' : [ authSelection - identity-provider-redirector,  authSelection - auth-username-password-form]
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) invoke authenticator.authenticate: identity-provider-redirector
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) authenticator ATTEMPTED: identity-provider-redirector
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) check execution: 'forms flow', requirement: 'ALTERNATIVE'
2025-06-18 15:09:18.678 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) processFlow: forms
2025-06-18 15:09:18.679 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) check execution: 'auth-username-password-form', requirement: 'REQUIRED'
2025-06-18 15:09:18.679 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) authenticator: auth-username-password-form
2025-06-18 15:09:18.679 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-12) Going through the flow 'browser' for adding executions
2025-06-18 15:09:18.679 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-12) Going through the flow 'forms' for adding executions
2025-06-18 15:09:18.679 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-12) Selections when trying execution 'auth-username-password-form' : [ authSelection - auth-username-password-form]
2025-06-18 15:09:18.679 | 2025-06-18 09:39:18,676 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-12) invoke authenticator.authenticate: auth-username-password-form
2025-06-18 15:09:18.688 | 2025-06-18 09:39:18,687 FINE  [freemarker.cache] (executor-thread-12) Couldn't find template in cache for "login.ftl"("en_US", UTF-8, parsed); will try to load it.
2025-06-18 15:09:18.688 | 2025-06-18 09:39:18,688 FINE  [freemarker.cache] (executor-thread-12) TemplateLoader.findTemplateSource("login_en_US.ftl"): Not found
2025-06-18 15:09:18.689 | 2025-06-18 09:39:18,689 FINE  [freemarker.cache] (executor-thread-12) TemplateLoader.findTemplateSource("login_en.ftl"): Not found
2025-06-18 15:09:18.690 | 2025-06-18 09:39:18,689 FINE  [freemarker.cache] (executor-thread-12) TemplateLoader.findTemplateSource("login.ftl"): Found
2025-06-18 15:09:18.690 | 2025-06-18 09:39:18,689 FINE  [freemarker.cache] (executor-thread-12) Loading template for "login.ftl"("en_US", UTF-8, parsed) from "jar:file:/opt/keycloak/lib/lib/main/org.keycloak.keycloak-themes-24.0.4.jar!/theme/base/login/login.ftl"
2025-06-18 15:09:18.700 | 2025-06-18 09:39:18,700 FINE  [freemarker.cache] (executor-thread-12) Couldn't find template in cache for "template.ftl"("en_US", UTF-8, parsed); will try to load it.
2025-06-18 15:09:18.701 | 2025-06-18 09:39:18,701 FINE  [freemarker.cache] (executor-thread-12) TemplateLoader.findTemplateSource("template_en_US.ftl"): Not found
2025-06-18 15:09:18.705 | 2025-06-18 09:39:18,705 FINE  [freemarker.cache] (executor-thread-12) TemplateLoader.findTemplateSource("template_en.ftl"): Not found
2025-06-18 15:09:18.706 | 2025-06-18 09:39:18,706 FINE  [freemarker.cache] (executor-thread-12) TemplateLoader.findTemplateSource("template.ftl"): Found
2025-06-18 15:09:18.707 | 2025-06-18 09:39:18,706 FINE  [freemarker.cache] (executor-thread-12) Loading template for "template.ftl"("en_US", UTF-8, parsed) from "jar:file:/opt/keycloak/lib/lib/main/org.keycloak.keycloak-themes-24.0.4.jar!/theme/base/login/template.ftl"
2025-06-18 15:09:18.723 | 2025-06-18 09:39:18,722 FINE  [freemarker.beans] (executor-thread-12) Key "selectedCredential" was not found on instance of org.keycloak.forms.login.freemarker.model.AuthenticationContextBean. Introspection information for the class is: {getClass=public final native java.lang.Class java.lang.Object.getClass(), getAuthenticationSelections=public java.util.List org.keycloak.forms.login.freemarker.model.AuthenticationContextBean.getAuthenticationSelections(), showResetCredentials=public boolean org.keycloak.forms.login.freemarker.model.AuthenticationContextBean.showResetCredentials(), java.lang.Object@bb7887c=freemarker.ext.beans.SimpleMethod@23a5bb9e, authenticationSelections=freemarker.ext.beans.FastPropertyDescriptor@5b4e56ac, java.lang.Object@79399de9={public java.lang.String java.lang.Object.toString()=[Ljava.lang.Class;@2dc72de0, public java.lang.String org.keycloak.forms.login.freemarker.model.AuthenticationContextBean.getAttemptedUsername()=[Ljava.lang.Class;@3c5089c1, public boolean org.keycloak.forms.login.freemarker.model.AuthenticationContextBean.showTryAnotherWayLink()=[Ljava.lang.Class;@3227ac9e, public boolean org.keycloak.forms.login.freemarker.model.AuthenticationContextBean.showResetCredentials()=[Ljava.lang.Class;@1bf57740, public java.util.List org.keycloak.forms.login.freemarker.model.AuthenticationContextBean.getAuthenticationSelections()=[Ljava.lang.Class;@f49e1f3, public final native java.lang.Class java.lang.Object.getClass()=[Ljava.lang.Class;@376f6f5a, public native int java.lang.Object.hashCode()=[Ljava.lang.Class;@38a36675, public boolean org.keycloak.forms.login.freemarker.model.AuthenticationContextBean.showUsername()=[Ljava.lang.Class;@8dc5956, public boolean java.lang.Object.equals(java.lang.Object)=[Ljava.lang.Class;@485a67c7}, showUsername=public boolean org.keycloak.forms.login.freemarker.model.AuthenticationContextBean.showUsername(), hashCode=public native int java.lang.Object.hashCode(), equals=public boolean java.lang.Object.equals(java.lang.Object), toString=public java.lang.String java.lang.Object.toString(), showTryAnotherWayLink=public boolean org.keycloak.forms.login.freemarker.model.AuthenticationContextBean.showTryAnotherWayLink(), attemptedUsername=freemarker.ext.beans.FastPropertyDescriptor@483d429, class=freemarker.ext.beans.FastPropertyDescriptor@27308a02, getAttemptedUsername=public java.lang.String org.keycloak.forms.login.freemarker.model.AuthenticationContextBean.getAttemptedUsername()}
2025-06-18 15:09:18.725 | 2025-06-18 09:39:18,725 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) JtaTransactionWrapper  commit
2025-06-18 15:09:18.726 | 2025-06-18 09:39:18,726 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) JtaTransactionWrapper end
2025-06-18 15:09:18.771 | 2025-06-18 09:39:18,771 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) new JtaTransactionWrapper
2025-06-18 15:09:18.771 | 2025-06-18 09:39:18,771 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) was existing? false
2025-06-18 15:09:18.773 | 2025-06-18 09:39:18,773 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) JtaTransactionWrapper  commit
2025-06-18 15:09:18.774 | 2025-06-18 09:39:18,773 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) JtaTransactionWrapper end
2025-06-18 15:09:18.790 | 2025-06-18 09:39:18,790 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) new JtaTransactionWrapper
2025-06-18 15:09:18.790 | 2025-06-18 09:39:18,790 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) was existing? false
2025-06-18 15:09:18.792 | 2025-06-18 09:39:18,791 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) JtaTransactionWrapper  commit
2025-06-18 15:09:18.797 | 2025-06-18 09:39:18,797 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) JtaTransactionWrapper end
2025-06-18 15:09:18.805 | 2025-06-18 09:39:18,805 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-17) new JtaTransactionWrapper
2025-06-18 15:09:18.806 | 2025-06-18 09:39:18,805 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-17) was existing? false
2025-06-18 15:09:18.807 | 2025-06-18 09:39:18,807 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-17) JtaTransactionWrapper  commit
2025-06-18 15:09:18.808 | 2025-06-18 09:39:18,808 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-17) JtaTransactionWrapper end
2025-06-18 15:09:18.809 | 2025-06-18 09:39:18,809 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) new JtaTransactionWrapper
2025-06-18 15:09:18.810 | 2025-06-18 09:39:18,810 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) was existing? false
2025-06-18 15:09:18.814 | 2025-06-18 09:39:18,814 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) JtaTransactionWrapper  commit
2025-06-18 15:09:18.825 | 2025-06-18 09:39:18,825 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) JtaTransactionWrapper end
2025-06-18 15:09:18.830 | 2025-06-18 09:39:18,830 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) new JtaTransactionWrapper
2025-06-18 15:09:18.841 | 2025-06-18 09:39:18,816 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-16) new JtaTransactionWrapper
2025-06-18 15:09:18.841 | 2025-06-18 09:39:18,841 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-16) was existing? false
2025-06-18 15:09:18.842 | 2025-06-18 09:39:18,820 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) new JtaTransactionWrapper
2025-06-18 15:09:18.842 | 2025-06-18 09:39:18,841 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) was existing? false
2025-06-18 15:09:18.842 | 2025-06-18 09:39:18,841 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) new JtaTransactionWrapper
2025-06-18 15:09:18.842 | 2025-06-18 09:39:18,842 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) was existing? false
2025-06-18 15:09:18.843 | 2025-06-18 09:39:18,843 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) JtaTransactionWrapper  commit
2025-06-18 15:09:18.844 | 2025-06-18 09:39:18,843 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-12) JtaTransactionWrapper end
2025-06-18 15:09:18.845 | 2025-06-18 09:39:18,844 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) JtaTransactionWrapper  commit
2025-06-18 15:09:18.845 | 2025-06-18 09:39:18,845 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-15) JtaTransactionWrapper end
2025-06-18 15:09:18.846 | 2025-06-18 09:39:18,841 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) was existing? false
2025-06-18 15:09:18.860 | 2025-06-18 09:39:18,856 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-16) JtaTransactionWrapper  commit
2025-06-18 15:09:18.860 | 2025-06-18 09:39:18,856 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-16) JtaTransactionWrapper end
2025-06-18 15:09:18.862 | 2025-06-18 09:39:18,862 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper  commit
2025-06-18 15:09:18.863 | 2025-06-18 09:39:18,863 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper end
2025-06-18 15:09:18.933 | 2025-06-18 09:39:18,933 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) new JtaTransactionWrapper
2025-06-18 15:09:18.933 | 2025-06-18 09:39:18,933 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) was existing? false
2025-06-18 15:09:18.935 | 2025-06-18 09:39:18,934 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper  commit
2025-06-18 15:09:18.935 | 2025-06-18 09:39:18,935 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper end
2025-06-18 15:09:19.004 | 2025-06-18 09:39:19,004 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) new JtaTransactionWrapper
2025-06-18 15:09:19.004 | 2025-06-18 09:39:19,004 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) was existing? false
2025-06-18 15:09:19.005 | 2025-06-18 09:39:19,005 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-16) new JtaTransactionWrapper
2025-06-18 15:09:19.006 | 2025-06-18 09:39:19,005 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper  commit
2025-06-18 15:09:19.006 | 2025-06-18 09:39:19,006 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper end
2025-06-18 15:09:19.008 | 2025-06-18 09:39:19,008 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-16) was existing? false
2025-06-18 15:09:19.021 | 2025-06-18 09:39:19,021 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) new JtaTransactionWrapper
2025-06-18 15:09:19.022 | 2025-06-18 09:39:19,022 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) was existing? false
2025-06-18 15:09:19.023 | 2025-06-18 09:39:19,023 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper  commit
2025-06-18 15:09:19.024 | 2025-06-18 09:39:19,022 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-16) JtaTransactionWrapper  commit
2025-06-18 15:09:19.025 | 2025-06-18 09:39:19,025 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-16) JtaTransactionWrapper end
2025-06-18 15:09:19.037 | 2025-06-18 09:39:19,037 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper end
2025-06-18 15:09:19.120 | 2025-06-18 09:39:19,120 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) new JtaTransactionWrapper
2025-06-18 15:09:19.121 | 2025-06-18 09:39:19,120 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) was existing? false
2025-06-18 15:09:19.124 | 2025-06-18 09:39:19,124 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper  commit
2025-06-18 15:09:19.125 | 2025-06-18 09:39:19,125 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper end
2025-06-18 15:09:21.040 | 2025-06-18 09:39:21,040 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (Timer-0) new JtaTransactionWrapper
2025-06-18 15:09:21.040 | 2025-06-18 09:39:21,040 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (Timer-0) was existing? false
2025-06-18 15:09:21.040 | 2025-06-18 09:39:21,040 DEBUG [org.keycloak.services.scheduled.ScheduledTaskRunner] (Timer-0) Executed scheduled task PropagateLastSessionRefreshTask
2025-06-18 15:09:21.049 | 2025-06-18 09:39:21,040 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (Timer-0) JtaTransactionWrapper  commit
2025-06-18 15:09:21.049 | 2025-06-18 09:39:21,040 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (Timer-0) JtaTransactionWrapper end
2025-06-18 15:09:21.164 | 2025-06-18 09:39:21,164 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) new JtaTransactionWrapper
2025-06-18 15:09:21.164 | 2025-06-18 09:39:21,164 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) was existing? false
2025-06-18 15:09:21.165 | 2025-06-18 09:39:21,164 DEBUG [io.quarkus.vertx.http.runtime.ForwardedParser] (executor-thread-18) Recalculated absoluteURI to http://localhost:8080/auth/realms/complychecker/login-actions/authenticate?session_code=nbGnHdwxoDDFFLv_H6-ibYgU4VUsCOtWMN0NdW7ECGI&execution=305b13e5-62e7-49e8-abe4-5267e55d379d&client_id=complychecker-frontend&tab_id=ju8uACaPu7Y
2025-06-18 15:09:21.166 | 2025-06-18 09:39:21,166 DEBUG [org.keycloak.services.resources.SessionCodeChecks] (executor-thread-18) Will use client 'complychecker-frontend' in back-to-application link
2025-06-18 15:09:21.167 | 2025-06-18 09:39:21,167 DEBUG [org.keycloak.services.managers.AuthenticationSessionManager] (executor-thread-18) Found AUTH_SESSION_ID cookie with value e279dfd6-bd7e-4b13-b6ad-b9fa215eb9d7
2025-06-18 15:09:21.168 | 2025-06-18 09:39:21,168 DEBUG [org.keycloak.authentication.AuthenticationProcessor] (executor-thread-18) authenticationAction
2025-06-18 15:09:21.168 | 2025-06-18 09:39:21,168 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-18) processAction: 305b13e5-62e7-49e8-abe4-5267e55d379d
2025-06-18 15:09:21.169 | 2025-06-18 09:39:21,169 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-18) Going through the flow 'browser' for adding executions
2025-06-18 15:09:21.169 | 2025-06-18 09:39:21,169 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-18) Going through the flow 'forms' for adding executions
2025-06-18 15:09:21.169 | 2025-06-18 09:39:21,169 DEBUG [org.keycloak.authentication.AuthenticationSelectionResolver] (executor-thread-18) Selections when trying execution 'auth-username-password-form' : [ authSelection - auth-username-password-form]
2025-06-18 15:09:21.170 | 2025-06-18 09:39:21,170 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-18) action: auth-username-password-form
2025-06-18 15:09:21.706 | 2025-06-18 09:39:21,706 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-18) authenticator SUCCESS: auth-username-password-form
2025-06-18 15:09:21.708 | 2025-06-18 09:39:21,708 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-18) check execution: 'forms flow', requirement: 'ALTERNATIVE'
2025-06-18 15:09:21.708 | 2025-06-18 09:39:21,708 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-18) processFlow: forms
2025-06-18 15:09:21.709 | 2025-06-18 09:39:21,708 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-18) check execution: 'auth-username-password-form', requirement: 'REQUIRED'
2025-06-18 15:09:21.709 | 2025-06-18 09:39:21,708 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-18) execution 'auth-username-password-form' is processed
2025-06-18 15:09:21.710 | 2025-06-18 09:39:21,710 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-18) Flow 'forms flow' successfully finished
2025-06-18 15:09:21.710 | 2025-06-18 09:39:21,710 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-18) processFlow: browser
2025-06-18 15:09:21.711 | 2025-06-18 09:39:21,711 DEBUG [org.keycloak.authentication.DefaultAuthenticationFlow] (executor-thread-18) Authentication successful of the top flow 'browser'
2025-06-18 15:09:21.729 | 2025-06-18 09:39:21,728 DEBUG [org.keycloak.services.managers.AuthenticationSessionManager] (executor-thread-18) Removing root authSession 'e279dfd6-bd7e-4b13-b6ad-b9fa215eb9d7'. Expire restart cookie: true
2025-06-18 15:09:21.740 | 2025-06-18 09:39:21,740 DEBUG [org.keycloak.protocol.oidc.OIDCLoginProtocol] (executor-thread-18) redirectAccessCode: state: c5789961-f2eb-4de1-b65a-0746b4a69fb1
2025-06-18 15:09:21.744 | 2025-06-18 09:39:21,743 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper  commit
2025-06-18 15:09:21.744 | 2025-06-18 09:39:21,744 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper end
2025-06-18 15:09:21.745 | 2025-06-18 09:39:21,745 DEBUG [org.keycloak.events] (executor-thread-18) type="LOGIN", realmId="0b7b842d-1441-40ea-9e6e-c1e54fbde8b6", clientId="complychecker-frontend", userId="4eb85cce-8784-4bf1-b50a-9ce3a80fb67b", sessionId="e279dfd6-bd7e-4b13-b6ad-b9fa215eb9d7", ipAddress="**********", auth_method="openid-connect", auth_type="code", response_type="code", redirect_uri="http://localhost:3000/", consent="no_consent_required", code_id="e279dfd6-bd7e-4b13-b6ad-b9fa215eb9d7", username="testuser", response_mode="fragment", authSessionParentId="e279dfd6-bd7e-4b13-b6ad-b9fa215eb9d7", authSessionTabId="ju8uACaPu7Y"
2025-06-18 15:09:23.279 | 2025-06-18 09:39:23,278 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) new JtaTransactionWrapper
2025-06-18 15:09:23.279 | 2025-06-18 09:39:23,279 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) was existing? false
2025-06-18 15:09:23.282 | 2025-06-18 09:39:23,279 DEBUG [io.quarkus.vertx.http.runtime.ForwardedParser] (executor-thread-18) Recalculated absoluteURI to http://localhost:8080/auth/realms/complychecker/protocol/openid-connect/3p-cookies/step1.html
2025-06-18 15:09:23.282 | 2025-06-18 09:39:23,280 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper  commit
2025-06-18 15:09:23.282 | 2025-06-18 09:39:23,280 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper end
2025-06-18 15:09:23.348 | 2025-06-18 09:39:23,348 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) new JtaTransactionWrapper
2025-06-18 15:09:23.348 | 2025-06-18 09:39:23,348 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) was existing? false
2025-06-18 15:09:23.349 | 2025-06-18 09:39:23,349 DEBUG [io.quarkus.vertx.http.runtime.ForwardedParser] (executor-thread-18) Recalculated absoluteURI to http://localhost:8080/auth/realms/complychecker/protocol/openid-connect/3p-cookies/step2.html
2025-06-18 15:09:23.351 | 2025-06-18 09:39:23,350 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper  commit
2025-06-18 15:09:23.351 | 2025-06-18 09:39:23,351 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper end
2025-06-18 15:09:23.428 | 2025-06-18 09:39:23,427 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) new JtaTransactionWrapper
2025-06-18 15:09:23.428 | 2025-06-18 09:39:23,427 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) was existing? false
2025-06-18 15:09:23.429 | 2025-06-18 09:39:23,428 DEBUG [io.quarkus.vertx.http.runtime.ForwardedParser] (executor-thread-18) Recalculated absoluteURI to http://localhost:8080/auth/realms/complychecker/protocol/openid-connect/token
2025-06-18 15:09:23.431 | 2025-06-18 09:39:23,430 DEBUG [org.keycloak.authentication.AuthenticationProcessor] (executor-thread-18) AUTHENTICATE CLIENT
2025-06-18 15:09:23.431 | 2025-06-18 09:39:23,431 DEBUG [org.keycloak.authentication.ClientAuthenticationFlow] (executor-thread-18) client authenticator: client-secret
2025-06-18 15:09:23.438 | 2025-06-18 09:39:23,431 DEBUG [org.keycloak.authentication.ClientAuthenticationFlow] (executor-thread-18) client authenticator SUCCESS: client-secret
2025-06-18 15:09:23.441 | 2025-06-18 09:39:23,431 DEBUG [org.keycloak.authentication.ClientAuthenticationFlow] (executor-thread-18) Client complychecker-frontend authenticated by client-secret
2025-06-18 15:09:23.442 | 2025-06-18 09:39:23,432 DEBUG [org.keycloak.models.sessions.infinispan.InfinispanUserSessionProvider] (executor-thread-18) getUserSessionWithPredicate(e279dfd6-bd7e-4b13-b6ad-b9fa215eb9d7): found in local cache
2025-06-18 15:09:23.442 | 2025-06-18 09:39:23,433 DEBUG [org.keycloak.protocol.oidc.utils.PkceUtils] (executor-thread-18) PKCE supporting Client, codeVerifier = 5GjhGNJ4MJY2MpmxuIdE5mOjBEP3WiJPQOSRvJrD5NCnnz2EH0xUQFjAd26tNPGK7eXiF4ScY2LyHupN7WwcvR2cqMYcskTm
2025-06-18 15:09:23.442 | 2025-06-18 09:39:23,433 DEBUG [org.keycloak.protocol.oidc.utils.PkceUtils] (executor-thread-18) PKCE codeChallengeMethod = S256
2025-06-18 15:09:23.442 | 2025-06-18 09:39:23,434 DEBUG [org.keycloak.protocol.oidc.utils.PkceUtils] (executor-thread-18) PKCE verification success. codeVerifierEncoded = kvQwDxM230rFRVgGsAJMFgGKzl_Yj9AtTJ4abEYt9tA, codeChallenge = kvQwDxM230rFRVgGsAJMFgGKzl_Yj9AtTJ4abEYt9tA
2025-06-18 15:09:23.466 | 2025-06-18 09:39:23,465 DEBUG [org.keycloak.services.cors.DefaultCors] (executor-thread-18) Invalid CORS request: origin http://localhost:3000 not in allowed origins [http://localhost:3000/]
2025-06-18 15:09:23.467 | 2025-06-18 09:39:23,466 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper  commit
2025-06-18 15:09:23.468 | 2025-06-18 09:39:23,468 DEBUG [org.keycloak.transaction.JtaTransactionWrapper] (executor-thread-18) JtaTransactionWrapper end
2025-06-18 15:09:23.469 | 2025-06-18 09:39:23,469 DEBUG [org.keycloak.events] (executor-thread-18) type="CODE_TO_TOKEN", realmId="0b7b842d-1441-40ea-9e6e-c1e54fbde8b6", clientId="complychecker-frontend", userId="4eb85cce-8784-4bf1-b50a-9ce3a80fb67b", sessionId="e279dfd6-bd7e-4b13-b6ad-b9fa215eb9d7", ipAddress="**********", token_id="808ea3cd-7537-4e97-860b-f68d8117b118", grant_type="authorization_code", refresh_token_type="Refresh", scope="openid email profile", refresh_token_id="0cfb4fa2-c131-45e5-8da0-ff5c595df17d", code_id="e279dfd6-bd7e-4b13-b6ad-b9fa215eb9d7", client_auth_method="client-secret"