import { URL } from 'url';
import { NucleiClient } from './nuclei-client';
import { SSLAnalyzer } from './ssl-analyzer';
import { ContentAnalyzer } from './content-analyzer';
import { HIPAA_SECURITY_CONSTANTS } from '../constants';
import {
  HipaaSecurityScanConfig,
  HipaaSecurityScanResult,
  ScannerConfig,
  RiskLevel,
  CategoryResult,
  HipaaTestFailure,
} from '../types';

export class HipaaSecurityScanner {
  private nucleiClient: NucleiClient;
  private sslAnalyzer: SSLAnalyzer;
  private contentAnalyzer: ContentAnalyzer;

  constructor(_config: ScannerConfig) {
    this.nucleiClient = new NucleiClient();
    this.sslAnalyzer = new SSLAnalyzer();
    this.contentAnalyzer = new ContentAnalyzer();
  }

  async performSecurityScan(scanConfig: HipaaSecurityScanConfig): Promise<HipaaSecurityScanResult> {
    const scanId = this.generateScanId();
    const startTime = Date.now();

    try {
      const url = new URL(scanConfig.targetUrl);
      const hostname = url.hostname;
      const port = url.port ? parseInt(url.port) : url.protocol === 'https:' ? 443 : 80;

      // Initialize scan result
      const scanResult: HipaaSecurityScanResult = {
        scanId,
        targetUrl: scanConfig.targetUrl,
        scanTimestamp: new Date(),
        scanDuration: 0,
        overallScore: 0,
        riskLevel: 'low',
        passedTests: [],
        failedTests: [],
        technicalSafeguards: this.initializeCategoryResult('technical'),
        administrativeSafeguards: this.initializeCategoryResult('administrative'),
        organizationalSafeguards: this.initializeCategoryResult('organizational'),
        physicalSafeguards: this.initializeCategoryResult('physical'),
        vulnerabilities: [],
        pagesScanned: [],
        toolsUsed: [],
        scanStatus: 'running',
      };

      // Perform SSL/TLS analysis if enabled
      if (scanConfig.enableSSLAnalysis) {
        await this.performSSLAnalysis(hostname, port, scanResult);
        scanResult.toolsUsed.push('SSL-Analyzer');
      }

      // Perform content analysis if enabled
      if (scanConfig.enableContentAnalysis) {
        await this.performContentAnalysis(scanConfig.targetUrl, scanResult);
        scanResult.toolsUsed.push('Content-Analyzer');
      }

      // Perform vulnerability scanning if enabled
      if (scanConfig.enableVulnerabilityScanning) {
        await this.performVulnerabilityScanning(scanConfig, scanResult);
        scanResult.toolsUsed.push('OWASP-ZAP');
      }

      // Calculate final scores and risk levels
      this.calculateFinalScores(scanResult);

      // Update scan duration and status
      scanResult.scanDuration = Date.now() - startTime;
      scanResult.scanStatus = 'completed';

      return scanResult;
    } catch (error) {
      const scanResult: HipaaSecurityScanResult = {
        scanId,
        targetUrl: scanConfig.targetUrl,
        scanTimestamp: new Date(),
        scanDuration: Date.now() - startTime,
        overallScore: 0,
        riskLevel: 'critical',
        passedTests: [],
        failedTests: [],
        technicalSafeguards: this.initializeCategoryResult('technical'),
        administrativeSafeguards: this.initializeCategoryResult('administrative'),
        organizationalSafeguards: this.initializeCategoryResult('organizational'),
        physicalSafeguards: this.initializeCategoryResult('physical'),
        vulnerabilities: [],
        pagesScanned: [],
        toolsUsed: [],
        scanStatus: 'failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error occurred',
      };

      return scanResult;
    }
  }

  private async performSSLAnalysis(
    hostname: string,
    port: number,
    scanResult: HipaaSecurityScanResult,
  ): Promise<void> {
    try {
      const sslResult = await this.sslAnalyzer.analyzeDomain(hostname, port);

      // Create test results based on SSL analysis
      if (sslResult.isValid) {
        scanResult.passedTests.push({
          testId: 'ssl_certificate_valid',
          testName: 'SSL Certificate Validity',
          hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
          description: 'SSL certificate is valid and properly configured',
          category: 'technical',
          passed: true,
          evidence: `Certificate valid for ${sslResult.daysRemaining} days, issued by ${sslResult.issuer}`,
          pagesTested: [scanResult.targetUrl],
          timestamp: new Date(),
        });
      } else {
        scanResult.failedTests.push({
          testId: 'ssl_certificate_invalid',
          testName: 'SSL Certificate Validity',
          hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
          description: 'SSL certificate validation failed',
          category: 'technical',
          passed: false,
          failureReason: 'Invalid or expired SSL certificate',
          riskLevel: 'critical',
          failureEvidence: [
            {
              location: scanResult.targetUrl,
              elementType: 'response',
              actualCode: `Certificate: ${sslResult.subject}`,
              expectedBehavior: 'Valid SSL certificate with proper configuration',
              context: `Issuer: ${sslResult.issuer}, Days remaining: ${sslResult.daysRemaining}`,
            },
          ],
          recommendedAction: 'Renew or fix SSL certificate configuration',
          remediationPriority: 1,
          timestamp: new Date(),
        });
      }

      // Add vulnerabilities from SSL analysis
      sslResult.vulnerabilities.forEach((vuln) => {
        scanResult.vulnerabilities.push({
          id: this.generateVulnerabilityId(),
          type: vuln.type,
          severity: vuln.severity,
          location: scanResult.targetUrl,
          description: vuln.description,
          evidence: { sslAnalysis: sslResult },
          remediationGuidance: vuln.remediation,
        });
      });
    } catch (error) {
      scanResult.failedTests.push({
        testId: 'ssl_analysis_error',
        testName: 'SSL Analysis',
        hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
        description: 'SSL analysis could not be completed',
        category: 'technical',
        passed: false,
        failureReason: error instanceof Error ? error.message : 'SSL analysis failed',
        riskLevel: 'high',
        failureEvidence: [
          {
            location: scanResult.targetUrl,
            elementType: 'response',
            actualCode: 'SSL analysis error',
            expectedBehavior: 'Successful SSL analysis',
            context: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendedAction: 'Investigate SSL configuration and connectivity',
        remediationPriority: 2,
        timestamp: new Date(),
      });
    }
  }

  private async performContentAnalysis(
    targetUrl: string,
    scanResult: HipaaSecurityScanResult,
  ): Promise<void> {
    try {
      // Use Nuclei HTTP client to access the URL and get content
      const response = await this.nucleiClient.fetchUrlContent(targetUrl);
      const contentResult = this.contentAnalyzer.analyzeContent(
        response.body,
        targetUrl,
        response.responseHeaders,
      );

      scanResult.pagesScanned.push(targetUrl);

      // Process ePHI detection results
      if (contentResult.hasEPHI) {
        contentResult.ephiMatches.forEach((match) => {
          scanResult.failedTests.push({
            testId: 'ephi_exposure',
            testName: 'ePHI Exposure Detection',
            hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.ACCESS_CONTROL,
            description: 'Potential ePHI exposure detected',
            category: 'technical',
            passed: false,
            failureReason: `Potential ePHI pattern detected: ${match.pattern}`,
            riskLevel: match.riskLevel,
            failureEvidence: [
              {
                location: match.location,
                elementType: 'html',
                actualCode: match.match,
                expectedBehavior: 'No ePHI should be exposed in web content',
                lineNumber: match.lineNumber,
                context: match.context,
              },
            ],
            recommendedAction: 'Remove or properly protect ePHI data',
            remediationPriority: match.riskLevel === 'critical' ? 1 : 2,
            timestamp: new Date(),
          });
        });
      }

      // Process security headers
      contentResult.securityHeaders.forEach((header) => {
        if (header.present && header.secure) {
          scanResult.passedTests.push({
            testId: `security_header_${header.header}`,
            testName: `Security Header: ${header.header}`,
            hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
            description: `Security header ${header.header} is properly configured`,
            category: 'technical',
            passed: true,
            evidence: `Header value: ${header.value}`,
            pagesTested: [targetUrl],
            timestamp: new Date(),
          });
        } else {
          scanResult.failedTests.push({
            testId: `security_header_${header.header}`,
            testName: `Security Header: ${header.header}`,
            hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
            description: `Security header ${header.header} is missing or misconfigured`,
            category: 'technical',
            passed: false,
            failureReason: header.present ? 'Header present but insecure' : 'Header missing',
            riskLevel: 'medium',
            failureEvidence: [
              {
                location: targetUrl,
                elementType: 'header',
                actualCode: header.value || 'Missing',
                expectedBehavior: header.recommendation || 'Secure header configuration',
                context: `Security header: ${header.header}`,
              },
            ],
            recommendedAction: header.recommendation || `Configure ${header.header} header`,
            remediationPriority: 3,
            timestamp: new Date(),
          });
        }
      });
    } catch (error) {
      scanResult.failedTests.push({
        testId: 'content_analysis_error',
        testName: 'Content Analysis',
        hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.ACCESS_CONTROL,
        description: 'Content analysis could not be completed',
        category: 'technical',
        passed: false,
        failureReason: error instanceof Error ? error.message : 'Content analysis failed',
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: 'Content analysis error',
            expectedBehavior: 'Successful content analysis',
            context: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendedAction: 'Investigate content accessibility and structure',
        remediationPriority: 3,
        timestamp: new Date(),
      });
    }
  }

  private async performVulnerabilityScanning(
    scanConfig: HipaaSecurityScanConfig,
    scanResult: HipaaSecurityScanResult,
  ): Promise<void> {
    try {
      console.log('🔍 Starting Nuclei-based vulnerability scanning...');

      // Discover URLs using Nuclei's HTTP client
      const discoveredUrls = await this.nucleiClient.discoverUrls(
        scanConfig.targetUrl,
        scanConfig.maxPages || 15,
      );

      // Add discovered URLs to scan result
      scanResult.pagesScanned.push(...discoveredUrls);

      // Perform Nuclei vulnerability scanning on discovered URLs
      const vulnerabilities = await this.nucleiClient.scanMultipleUrls(discoveredUrls, {
        timeout: scanConfig.timeout || 30000,
        tags: ['ssl', 'tls', 'headers', 'privacy', 'disclosure', 'auth', 'session'],
        severity: ['critical', 'high', 'medium', 'low'],
      });

      // Convert Nuclei vulnerabilities to our format
      vulnerabilities.forEach((vuln) => {
        scanResult.vulnerabilities.push({
          id: vuln.id,
          type: vuln.type,
          severity: vuln.severity,
          location: vuln.location,
          description: vuln.description,
          evidence: vuln.evidence,
          remediationGuidance: vuln.remediationGuidance,
        });
      });

      console.log(
        `🔍 Found ${vulnerabilities.length} vulnerabilities across ${discoveredUrls.length} URLs`,
      );
    } catch (error) {
      // Handle Nuclei scanning errors gracefully
      console.error('❌ Nuclei vulnerability scanning failed:', error);

      scanResult.failedTests.push({
        testId: 'vulnerability_scan_error',
        testName: 'Vulnerability Scanning',
        hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.AUDIT_CONTROLS,
        description: 'Vulnerability scanning could not be completed',
        category: 'technical',
        passed: false,
        failureReason: error instanceof Error ? error.message : 'Vulnerability scanning failed',
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: scanConfig.targetUrl,
            elementType: 'response',
            actualCode: 'Vulnerability scan error',
            expectedBehavior: 'Successful vulnerability scan',
            context: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendedAction: 'Check Nuclei installation and configuration',
        remediationPriority: 3,
        timestamp: new Date(),
      });
    }
  }

  private calculateFinalScores(scanResult: HipaaSecurityScanResult): void {
    const totalTests = scanResult.passedTests.length + scanResult.failedTests.length;
    const passedTests = scanResult.passedTests.length;

    // Calculate overall score (0-100)
    scanResult.overallScore = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;

    // Determine risk level based on failed tests and vulnerabilities
    const criticalIssues =
      scanResult.failedTests.filter((t) => t.riskLevel === 'critical').length +
      scanResult.vulnerabilities.filter((v) => v.severity === 'critical').length;
    const highIssues =
      scanResult.failedTests.filter((t) => t.riskLevel === 'high').length +
      scanResult.vulnerabilities.filter((v) => v.severity === 'high').length;

    if (criticalIssues > 0) {
      scanResult.riskLevel = 'critical';
    } else if (highIssues > 0) {
      scanResult.riskLevel = 'high';
    } else if (scanResult.overallScore < 70) {
      scanResult.riskLevel = 'medium';
    } else {
      scanResult.riskLevel = 'low';
    }

    // Update category results
    this.updateCategoryResults(scanResult);
  }

  private updateCategoryResults(scanResult: HipaaSecurityScanResult): void {
    const categories = ['technical', 'administrative', 'organizational', 'physical'] as const;

    categories.forEach((category) => {
      const categoryTests = [
        ...scanResult.passedTests.filter((t) => t.category === category),
        ...scanResult.failedTests.filter((t) => t.category === category),
      ];

      const categoryPassed = scanResult.passedTests.filter((t) => t.category === category);
      const categoryFailed = scanResult.failedTests.filter((t) => t.category === category);

      const categoryResult: CategoryResult = {
        category,
        totalTests: categoryTests.length,
        passedTests: categoryPassed.length,
        failedTests: categoryFailed.length,
        score:
          categoryTests.length > 0
            ? Math.round((categoryPassed.length / categoryTests.length) * 100)
            : 0,
        riskLevel: this.calculateCategoryRisk(categoryFailed),
        criticalIssues: categoryFailed.filter((t) => t.riskLevel === 'critical').length,
        highIssues: categoryFailed.filter((t) => t.riskLevel === 'high').length,
        mediumIssues: categoryFailed.filter((t) => t.riskLevel === 'medium').length,
        lowIssues: categoryFailed.filter((t) => t.riskLevel === 'low').length,
      };

      switch (category) {
        case 'technical':
          scanResult.technicalSafeguards = categoryResult;
          break;
        case 'administrative':
          scanResult.administrativeSafeguards = categoryResult;
          break;
        case 'organizational':
          scanResult.organizationalSafeguards = categoryResult;
          break;
        case 'physical':
          scanResult.physicalSafeguards = categoryResult;
          break;
      }
    });
  }

  private calculateCategoryRisk(failedTests: HipaaTestFailure[]): RiskLevel {
    if (failedTests.some((t) => t.riskLevel === 'critical')) return 'critical';
    if (failedTests.some((t) => t.riskLevel === 'high')) return 'high';
    if (failedTests.some((t) => t.riskLevel === 'medium')) return 'medium';
    return 'low';
  }

  private initializeCategoryResult(
    category: 'technical' | 'administrative' | 'organizational' | 'physical',
  ): CategoryResult {
    return {
      category,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      score: 0,
      riskLevel: 'low',
      criticalIssues: 0,
      highIssues: 0,
      mediumIssues: 0,
      lowIssues: 0,
    };
  }

  private generateScanId(): string {
    return `hipaa-security-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  private generateVulnerabilityId(): string {
    return `vuln-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  async cleanup(): Promise<void> {
    // No cleanup needed for Nuclei client
    console.log('🧹 HIPAA Security Scanner cleanup completed');
  }
}
