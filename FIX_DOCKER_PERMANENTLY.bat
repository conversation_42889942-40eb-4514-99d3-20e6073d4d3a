@echo off
echo.
echo 🔧 PERMANENT DOCKER WSL2 FIX - ONE-CLICK SOLUTION
echo =================================================
echo.
echo This script will:
echo ✅ Fix the current WSL2 network issue
echo ✅ Prevent the issue from happening again
echo ✅ Set up automatic monitoring
echo ✅ Optimize Docker performance
echo.

REM Check if running as Administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ADMINISTRATOR RIGHTS REQUIRED
    echo.
    echo Please right-click this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo ✅ Running with Administrator privileges
echo.

echo 🚀 Starting permanent fix process...
echo.

REM Run the permanent fix
echo ⏳ Running permanent WSL2 fix...
call "%~dp0scripts\permanent-wsl2-fix.bat"

echo.
echo ⏳ Setting up automatic prevention...
call "%~dp0scripts\setup-auto-prevention.bat"

echo.
echo 🎉 PERMANENT FIX COMPLETED!
echo ===========================
echo.
echo ✅ WSL2 network issue fixed
echo ✅ Docker Desktop should now start properly
echo ✅ Automatic prevention measures installed
echo ✅ Future issues will be prevented
echo.

echo 📋 What was fixed:
echo   • WSL2 network shares (\\wsl$)
echo   • Docker WSL distributions
echo   • Windows service configuration
echo   • Network stack optimization
echo   • Registry entries
echo   • Automatic monitoring setup
echo.

echo 🛡️ Prevention measures active:
echo   • Startup task scheduler entry
echo   • Service dependency configuration
echo   • Optimized WSL configuration
echo   • Network service hardening
echo.

echo 🚀 Next steps:
echo   1. Docker Desktop should start normally now
echo   2. Run: docker-compose up -d (in project directory)
echo   3. Your HIPAA security system is ready!
echo.

echo 💡 If you ever encounter issues again:
echo   • Run: scripts\docker-startup-guardian.bat
echo   • Or re-run this fix: FIX_DOCKER_PERMANENTLY.bat
echo.

set /p start_docker="Would you like to start Docker Desktop now? (y/N): "
if /i "%start_docker%"=="y" (
    echo.
    echo 🚀 Starting Docker Desktop...
    
    if exist "%ProgramFiles%\Docker\Docker\Docker Desktop.exe" (
        start "" "%ProgramFiles%\Docker\Docker\Docker Desktop.exe"
        echo ✅ Docker Desktop started
        echo.
        echo ⏳ Please wait 2-3 minutes for Docker to fully initialize
        echo Then you can run: docker-compose up -d
    ) else if exist "%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe" (
        start "" "%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe"
        echo ✅ Docker Desktop started
        echo.
        echo ⏳ Please wait 2-3 minutes for Docker to fully initialize
        echo Then you can run: docker-compose up -d
    ) else (
        echo ❌ Docker Desktop executable not found
        echo Please start Docker Desktop manually
    )
)

echo.
echo 🎯 PERMANENT SOLUTION COMPLETE!
echo.
echo Your Docker environment is now permanently fixed and optimized.
echo The WSL2 network issue should never occur again.
echo.
pause
