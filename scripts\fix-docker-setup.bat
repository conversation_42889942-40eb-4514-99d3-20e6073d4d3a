@echo off
echo 🐳 Docker Setup and Troubleshooting Script
echo ==========================================

echo 🔍 Checking Docker status...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed or not in PATH
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker is installed

docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Docker is not running
    echo Please start Docker Desktop and run this script again
    pause
    exit /b 1
)

echo ✅ Docker is running

echo 🧹 Cleaning up existing containers...
docker-compose down --remove-orphans >nul 2>&1

echo 📋 Validating Docker Compose configuration...
docker-compose config >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose configuration has errors
    docker-compose config
    pause
    exit /b 1
)

echo ✅ Docker Compose configuration is valid

echo 🏗️ Building backend image...
docker-compose build backend
if %errorlevel% neq 0 (
    echo ❌ Backend build failed
    pause
    exit /b 1
)

echo ✅ Backend built successfully

echo 🚀 Starting services...
docker-compose up -d
if %errorlevel% neq 0 (
    echo ❌ Failed to start services
    echo Checking logs...
    docker-compose logs
    pause
    exit /b 1
)

echo ✅ Services started successfully

echo ⏳ Waiting for services to initialize...
timeout /t 10 /nobreak >nul

echo 📊 Checking service status...
docker-compose ps

echo.
echo 🎉 Docker setup completed!
echo.
echo 📋 Service URLs:
echo   Backend API:     http://localhost:3001
echo   Keycloak:        http://localhost:8080/auth
echo   MailHog:         http://localhost:8025
echo   PostgreSQL:      localhost:5432
echo.
echo 💡 Next steps:
echo   1. Start the frontend: cd frontend ^&^& npm run dev
echo   2. Check logs: docker-compose logs -f
echo   3. Stop services: docker-compose down
echo.
pause
