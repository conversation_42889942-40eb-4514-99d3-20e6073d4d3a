'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';

import {
  Loader2,
  Shield,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Play,
  Settings,
} from 'lucide-react';
import { HipaaSecurityResultsPage } from '@/components/hipaa-security';
import { HipaaSecurityScanResult } from '@/types/hipaa-security';

interface ScanConfig {
  targetUrl: string;
  maxPages: number;
  scanDepth: number;
  timeout: number;
  enableVulnerabilityScanning: boolean;
  enableSSLAnalysis: boolean;
  enableContentAnalysis: boolean;
}

interface ScanResponse {
  success: boolean;
  data: {
    scanId: string;
    status: string;
    message: string;
    result?: HipaaSecurityScanResult;
  };
  error?: string;
}

export default function HipaaSecurityLivePage() {
  const [scanConfig, setScanConfig] = useState<ScanConfig>({
    targetUrl: '',
    maxPages: 15,
    scanDepth: 2,
    timeout: 300000,
    enableVulnerabilityScanning: true,
    enableSSLAnalysis: true,
    enableContentAnalysis: true,
  });

  const [isScanning, setIsScanning] = useState(false);
  const [scanResult, setScanResult] = useState<HipaaSecurityScanResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleInputChange = (field: keyof ScanConfig, value: string | number | boolean) => {
    setScanConfig((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateUrl = (url: string): boolean => {
    try {
      new URL(url);
      return url.startsWith('http://') || url.startsWith('https://');
    } catch {
      return false;
    }
  };

  const startScan = async () => {
    if (!validateUrl(scanConfig.targetUrl)) {
      setError('Please enter a valid URL (must start with http:// or https://)');
      return;
    }

    setIsScanning(true);
    setError(null);
    setScanResult(null);

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api/v1';
      const response = await fetch(`${apiUrl}/hipaa-security/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scanConfig),
      });

      const data: ScanResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      if (data.success && data.data.result) {
        setScanResult(data.data.result);
      } else {
        throw new Error(data.data.message || 'Scan completed but no results returned');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsScanning(false);
    }
  };

  const handleExportReport = () => {
    if (scanResult) {
      const dataStr = JSON.stringify(scanResult, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `hipaa-security-report-${scanResult.scanId}.json`;
      link.click();
      URL.revokeObjectURL(url);
    }
  };

  const handleStartNewScan = () => {
    setScanResult(null);
    setError(null);
    setScanConfig((prev) => ({ ...prev, targetUrl: '' }));
  };

  if (scanResult) {
    return (
      <div>
        <div className="bg-green-50 p-4 mb-6 rounded-lg">
          <h2 className="text-lg font-bold text-green-800 mb-2">
            &#x2705; HIPAA Security Scan Complete
          </h2>
          <p className="text-green-700 text-sm">
            Live scan results for: <strong>{scanResult.targetUrl}</strong>
          </p>
        </div>

        <HipaaSecurityResultsPage
          scanResult={scanResult}
          onExportReport={handleExportReport}
          onStartNewScan={handleStartNewScan}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <Shield className="h-8 w-8 text-blue-600" />
          <h1 className="text-4xl font-bold text-gray-900">Live HIPAA Security Scanner</h1>
        </div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Test your website&apos;s HIPAA Security compliance with our advanced scanning engine. Get
          detailed technical, administrative, and physical safeguards analysis.
        </p>
      </div>

      {/* Scan Configuration */}
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Scan Configuration</span>
          </CardTitle>
          <CardDescription>
            Configure your HIPAA Security compliance scan parameters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* URL Input */}
          <div className="space-y-2">
            <Label htmlFor="targetUrl">Target URL *</Label>
            <Input
              id="targetUrl"
              type="url"
              placeholder="https://example.com"
              value={scanConfig.targetUrl}
              onChange={(e) => handleInputChange('targetUrl', e.target.value)}
              className="text-lg"
            />
            <p className="text-sm text-gray-600 mb-4">
              Get started with a free HIPAA Security Rule compliance scan. Get results for HIPAA
              Security compliance
            </p>
          </div>

          {/* Advanced Options Toggle */}
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => setShowAdvanced(!showAdvanced)}>
              {showAdvanced ? 'Hide' : 'Show'} Advanced Options
            </Button>
          </div>

          {/* Advanced Options */}
          {showAdvanced && (
            <div className="space-y-4 p-4 bg-gray-50 border rounded-lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Advanced Configuration</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maxPages" className="text-sm font-medium text-gray-700">
                    Max Pages to Scan
                  </Label>
                  <Input
                    id="maxPages"
                    type="number"
                    min="1"
                    max="50"
                    value={scanConfig.maxPages}
                    onChange={(e) => handleInputChange('maxPages', parseInt(e.target.value))}
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500">Number of pages to scan (1-50)</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="scanDepth" className="text-sm font-medium text-gray-700">
                    Scan Depth
                  </Label>
                  <Input
                    id="scanDepth"
                    type="number"
                    min="1"
                    max="5"
                    value={scanConfig.scanDepth}
                    onChange={(e) => handleInputChange('scanDepth', parseInt(e.target.value))}
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500">How deep to crawl links (1-5)</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeout" className="text-sm font-medium text-gray-700">
                  Scan Timeout (milliseconds)
                </Label>
                <Input
                  id="timeout"
                  type="number"
                  min="60000"
                  max="1800000"
                  step="30000"
                  value={scanConfig.timeout}
                  onChange={(e) => handleInputChange('timeout', parseInt(e.target.value))}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  Maximum time for scan completion (60s - 30min)
                </p>
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">Scan Features</Label>
                <div className="space-y-3">
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={scanConfig.enableVulnerabilityScanning}
                      onChange={(e) => {
                        handleInputChange('enableVulnerabilityScanning', e.target.checked);
                      }}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <div>
                      <span className="text-sm font-medium text-gray-900">
                        Vulnerability Scanning
                      </span>
                      <p className="text-xs text-gray-500">
                        Scan for security vulnerabilities using ZAP
                      </p>
                    </div>
                  </label>
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={scanConfig.enableSSLAnalysis}
                      onChange={(e) => handleInputChange('enableSSLAnalysis', e.target.checked)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <div>
                      <span className="text-sm font-medium text-gray-900">SSL/TLS Analysis</span>
                      <p className="text-xs text-gray-500">
                        Analyze SSL certificates and encryption
                      </p>
                    </div>
                  </label>
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={scanConfig.enableContentAnalysis}
                      onChange={(e) => handleInputChange('enableContentAnalysis', e.target.checked)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <div>
                      <span className="text-sm font-medium text-gray-900">Content Analysis</span>
                      <p className="text-xs text-gray-500">
                        Analyze page content for ePHI and security issues
                      </p>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <span className="text-red-800 font-medium">Scan Error</span>
              </div>
              <p className="text-red-700 mt-1">{error}</p>
            </div>
          )}

          {/* Scan Button */}
          <Button
            onClick={startScan}
            disabled={isScanning || !scanConfig.targetUrl}
            className="w-full h-12 text-lg"
          >
            {isScanning ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Scanning... This may take a few minutes
              </>
            ) : (
              <>
                <Play className="mr-2 h-5 w-5" />
                Start HIPAA Security Scan
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Features Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 mb-3">
              <Shield className="h-5 w-5 text-blue-500" />
              <h3 className="font-semibold">Technical Safeguards</h3>
            </div>
            <p className="text-sm text-gray-600">
              Access controls, audit controls, integrity, authentication, and transmission security
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 mb-3">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <h3 className="font-semibold">Administrative Safeguards</h3>
            </div>
            <p className="text-sm text-gray-600">
              Security management, workforce training, information access management
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 mb-3">
              <ExternalLink className="h-5 w-5 text-purple-500" />
              <h3 className="font-semibold">Physical Safeguards</h3>
            </div>
            <p className="text-sm text-gray-600">
              Facility access controls, workstation use, device and media controls
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
