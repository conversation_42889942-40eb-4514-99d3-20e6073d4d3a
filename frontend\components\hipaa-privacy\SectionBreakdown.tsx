import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { 
  Shield, 
  FileText, 
  Users,
  Scale,
  TrendingUp,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import { 
  HipaaPrivacyCheckResult,
  HipaaPrivacyScanResult,
  SectionBreakdownProps,
  HipaaPrivacyCheckCategory
} from '@/types/hipaa-privacy';

export const SectionBreakdown: React.FC<SectionBreakdownProps> = ({
  checks,
  scanResult,
}) => {
  // Group checks by category
  const checksByCategory = checks.reduce((acc, check) => {
    if (!acc[check.category]) {
      acc[check.category] = [];
    }
    acc[check.category].push(check);
    return acc;
  }, {} as Record<HipaaPrivacyCheckCategory, HipaaPrivacyCheckResult[]>);

  const getCategoryDisplayName = (category: HipaaPrivacyCheckCategory): string => {
    switch (category) {
      case HipaaPrivacyCheckCategory.PRESENCE:
        return 'Privacy Policy Presence';
      case HipaaPrivacyCheckCategory.ACCESSIBILITY:
        return 'Accessibility & Availability';
      case HipaaPrivacyCheckCategory.CONTENT_STRUCTURE:
        return 'Content Structure & Organization';
      case HipaaPrivacyCheckCategory.HIPAA_SPECIFIC:
        return 'HIPAA-Specific Requirements';
      case HipaaPrivacyCheckCategory.QUALITY:
        return 'Content Quality & Readability';
      case HipaaPrivacyCheckCategory.LEGAL_COMPLIANCE:
        return 'Legal Compliance & Accuracy';
      case HipaaPrivacyCheckCategory.CONTACT_INFO:
        return 'Contact Information & Support';
      default:
        return category.replace('_', ' ');
    }
  };

  const getCategoryDescription = (category: HipaaPrivacyCheckCategory): string => {
    switch (category) {
      case HipaaPrivacyCheckCategory.PRESENCE:
        return 'Verifies that a privacy policy exists and is properly linked from the website';
      case HipaaPrivacyCheckCategory.ACCESSIBILITY:
        return 'Ensures the privacy policy is easily accessible and available to users';
      case HipaaPrivacyCheckCategory.CONTENT_STRUCTURE:
        return 'Checks for proper organization and structure of privacy policy content';
      case HipaaPrivacyCheckCategory.HIPAA_SPECIFIC:
        return 'Validates HIPAA-specific requirements and protected health information handling';
      case HipaaPrivacyCheckCategory.QUALITY:
        return 'Assesses content quality, readability, and clarity for users';
      case HipaaPrivacyCheckCategory.LEGAL_COMPLIANCE:
        return 'Ensures legal accuracy and compliance with HIPAA regulations';
      case HipaaPrivacyCheckCategory.CONTACT_INFO:
        return 'Verifies availability of contact information for privacy-related inquiries';
      default:
        return 'Additional compliance checks for this category';
    }
  };

  const getCategoryIcon = (category: HipaaPrivacyCheckCategory) => {
    switch (category) {
      case HipaaPrivacyCheckCategory.PRESENCE:
        return <FileText className="h-6 w-6" />;
      case HipaaPrivacyCheckCategory.ACCESSIBILITY:
        return <Users className="h-6 w-6" />;
      case HipaaPrivacyCheckCategory.CONTENT_STRUCTURE:
        return <TrendingUp className="h-6 w-6" />;
      case HipaaPrivacyCheckCategory.HIPAA_SPECIFIC:
        return <Shield className="h-6 w-6" />;
      case HipaaPrivacyCheckCategory.QUALITY:
        return <CheckCircle className="h-6 w-6" />;
      case HipaaPrivacyCheckCategory.LEGAL_COMPLIANCE:
        return <Scale className="h-6 w-6" />;
      case HipaaPrivacyCheckCategory.CONTACT_INFO:
        return <Users className="h-6 w-6" />;
      default:
        return <FileText className="h-6 w-6" />;
    }
  };

  const getCategoryColor = (category: HipaaPrivacyCheckCategory): string => {
    switch (category) {
      case HipaaPrivacyCheckCategory.PRESENCE:
        return 'border-purple-200 bg-purple-50';
      case HipaaPrivacyCheckCategory.ACCESSIBILITY:
        return 'border-blue-200 bg-blue-50';
      case HipaaPrivacyCheckCategory.CONTENT_STRUCTURE:
        return 'border-green-200 bg-green-50';
      case HipaaPrivacyCheckCategory.HIPAA_SPECIFIC:
        return 'border-red-200 bg-red-50';
      case HipaaPrivacyCheckCategory.QUALITY:
        return 'border-yellow-200 bg-yellow-50';
      case HipaaPrivacyCheckCategory.LEGAL_COMPLIANCE:
        return 'border-orange-200 bg-orange-50';
      case HipaaPrivacyCheckCategory.CONTACT_INFO:
        return 'border-teal-200 bg-teal-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const calculateCategoryStats = (categoryChecks: HipaaPrivacyCheckResult[]) => {
    const total = categoryChecks.length;
    const passed = categoryChecks.filter(check => check.passed).length;
    const failed = total - passed;
    const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
    
    const criticalIssues = categoryChecks.filter(check => !check.passed && check.severity === 'critical').length;
    const highIssues = categoryChecks.filter(check => !check.passed && check.severity === 'high').length;
    const mediumIssues = categoryChecks.filter(check => !check.passed && check.severity === 'medium').length;
    const lowIssues = categoryChecks.filter(check => !check.passed && check.severity === 'low').length;

    return {
      total,
      passed,
      failed,
      percentage,
      criticalIssues,
      highIssues,
      mediumIssues,
      lowIssues
    };
  };

  return (
    <div className="space-y-6">
      {Object.entries(checksByCategory).map(([category, categoryChecks]) => {
        const categoryEnum = category as HipaaPrivacyCheckCategory;
        const stats = calculateCategoryStats(categoryChecks);
        
        return (
          <Card key={category} className={`border-2 ${getCategoryColor(categoryEnum)}`}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getCategoryIcon(categoryEnum)}
                  <div>
                    <CardTitle className="text-xl">
                      {getCategoryDisplayName(categoryEnum)}
                    </CardTitle>
                    <p className="text-sm text-gray-600 mt-1">
                      {getCategoryDescription(categoryEnum)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold">
                    {stats.percentage}%
                  </div>
                  <div className="text-sm text-gray-600">
                    {stats.passed}/{stats.total} passed
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Progress Bar */}
                <div>
                  <Progress value={stats.percentage} className="h-3" />
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-white rounded border">
                    <div className="text-lg font-bold text-green-600">{stats.passed}</div>
                    <div className="text-xs text-gray-600">Passed</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded border">
                    <div className="text-lg font-bold text-red-600">{stats.failed}</div>
                    <div className="text-xs text-gray-600">Failed</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded border">
                    <div className="text-lg font-bold text-red-600">{stats.criticalIssues}</div>
                    <div className="text-xs text-gray-600">Critical</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded border">
                    <div className="text-lg font-bold text-orange-600">{stats.highIssues}</div>
                    <div className="text-xs text-gray-600">High</div>
                  </div>
                </div>

                {/* Individual Checks Summary */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">
                    Individual Checks ({categoryChecks.length})
                  </h4>
                  <div className="space-y-2">
                    {categoryChecks.map((check) => (
                      <div key={check.checkId} className="flex items-center justify-between p-2 bg-white rounded border">
                        <div className="flex items-center gap-2">
                          {check.passed ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                          <span className="text-sm font-medium">{check.checkName}</span>
                          {check.hipaaReference && (
                            <Badge variant="outline" className="text-xs">
                              {check.hipaaReference}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant={check.passed ? 'default' : 'destructive'}
                            className="text-xs"
                          >
                            {check.severity.toUpperCase()}
                          </Badge>
                          <span className="text-sm font-medium">
                            {check.score}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Category-specific Recommendations */}
                {stats.failed > 0 && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm font-semibold text-yellow-800">
                        Category Recommendations
                      </span>
                    </div>
                    <p className="text-sm text-yellow-700">
                      {stats.failed} check{stats.failed > 1 ? 's' : ''} failed in this category. 
                      Focus on addressing {stats.criticalIssues > 0 ? 'critical' : stats.highIssues > 0 ? 'high' : 'medium'} 
                      priority issues first to improve compliance.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
