import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { HipaaTestDetail, HipaaTestFailure, RiskLevel } from '@/types/hipaa-security';
import { FailureEvidenceDisplay } from './FailureEvidenceDisplay';

interface TestResultsListProps {
  tests: (HipaaTestDetail | HipaaTestFailure)[];
  showFailureDetails: boolean;
}

export const TestResultsList: React.FC<TestResultsListProps> = ({ tests, showFailureDetails }) => {
  const [expandedTests, setExpandedTests] = useState<Set<string>>(new Set());

  const toggleExpanded = (testId: string) => {
    const newExpanded = new Set(expandedTests);
    if (newExpanded.has(testId)) {
      newExpanded.delete(testId);
    } else {
      newExpanded.add(testId);
    }
    setExpandedTests(newExpanded);
  };

  const getRiskLevelColor = (riskLevel: RiskLevel): string => {
    switch (riskLevel) {
      case 'critical':
        return 'bg-red-500 text-white';
      case 'high':
        return 'bg-orange-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-black';
      case 'low':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getTestIcon = (test: HipaaTestDetail | HipaaTestFailure) => {
    if (test.passed) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else {
      const failedTest = test as HipaaTestFailure;
      switch (failedTest.riskLevel) {
        case 'critical':
        case 'high':
          return <XCircle className="h-5 w-5 text-red-500" />;
        case 'medium':
          return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
        case 'low':
          return <AlertTriangle className="h-5 w-5 text-green-500" />;
        default:
          return <XCircle className="h-5 w-5 text-gray-500" />;
      }
    }
  };

  if (tests.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">No tests in this category.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {tests.map((test) => {
        const isExpanded = expandedTests.has(test.testId);
        const isFailedTest = !test.passed;

        return (
          <Card key={test.testId} className={isFailedTest ? 'border-red-200' : 'border-green-200'}>
            <Collapsible>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getTestIcon(test)}
                      <div>
                        <CardTitle className="text-xl font-bold text-gray-900 leading-tight">
                          {test.testName}
                        </CardTitle>
                        <p className="text-sm text-gray-600 mt-1 mb-3 leading-relaxed">
                          {test.description}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="outline" className="text-xs font-medium px-2 py-1">
                            {test.hipaaSection}
                          </Badge>
                          <Badge
                            variant="secondary"
                            className="text-xs font-medium px-2 py-1 capitalize"
                          >
                            {test.category}
                          </Badge>
                          {isFailedTest && (
                            <Badge
                              className={`${getRiskLevelColor((test as HipaaTestFailure).riskLevel)} text-white text-xs font-semibold px-2 py-1`}
                            >
                              {(test as HipaaTestFailure).riskLevel.toUpperCase()} RISK
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => toggleExpanded(test.testId)}>
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardHeader>
              </CollapsibleTrigger>

              <CollapsibleContent>
                <CardContent>
                  <div className="space-y-4">
                    <p className="text-gray-700">{test.description}</p>

                    {test.passed ? (
                      <div className="bg-green-50 border border-green-200 p-5 rounded-lg">
                        <h4 className="font-bold text-green-800 mb-3 text-lg flex items-center gap-2">
                          <CheckCircle className="h-5 w-5" />
                          Test Passed Successfully
                        </h4>
                        <p className="text-green-700 leading-relaxed text-base mb-4">
                          {(test as HipaaTestDetail).evidence}
                        </p>
                        {(test as HipaaTestDetail).pagesTested.length > 0 && (
                          <div className="mt-4">
                            <span className="text-sm font-bold text-green-800 block mb-2">
                              Pages Tested ({(test as HipaaTestDetail).pagesTested.length}):
                            </span>
                            <div className="flex flex-wrap gap-2">
                              {(test as HipaaTestDetail).pagesTested.map((page, index) => (
                                <Badge
                                  key={index}
                                  variant="outline"
                                  className="text-xs font-medium px-2 py-1"
                                >
                                  {page}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="bg-red-50 border border-red-200 p-5 rounded-lg">
                        <h4 className="font-bold text-red-800 mb-3 text-lg flex items-center gap-2">
                          <XCircle className="h-5 w-5" />
                          Test Failed
                        </h4>
                        <div className="bg-white border border-red-200 p-4 rounded-lg mb-4">
                          <h5 className="font-semibold text-red-800 mb-2">Failure Reason:</h5>
                          <p className="text-red-700 leading-relaxed text-base">
                            {(test as HipaaTestFailure).failureReason}
                          </p>
                        </div>

                        {showFailureDetails &&
                          (test as HipaaTestFailure).failureEvidence.length > 0 && (
                            <div className="mb-4">
                              <FailureEvidenceDisplay
                                evidence={(test as HipaaTestFailure).failureEvidence}
                              />
                            </div>
                          )}

                        <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                          <h5 className="font-bold text-blue-800 mb-2 flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4" />
                            Recommended Action:
                          </h5>
                          <p className="text-blue-700 leading-relaxed text-base mb-3">
                            {(test as HipaaTestFailure).recommendedAction}
                          </p>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="font-medium">
                              Priority: {(test as HipaaTestFailure).remediationPriority}/5
                            </Badge>
                            <Badge
                              className={getRiskLevelColor((test as HipaaTestFailure).riskLevel)}
                            >
                              {(test as HipaaTestFailure).riskLevel.toUpperCase()} RISK
                            </Badge>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        );
      })}
    </div>
  );
};
