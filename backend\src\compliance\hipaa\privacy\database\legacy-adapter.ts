// backend/src/compliance/hipaa/privacy/database/legacy-adapter.ts

import knex from '../../../../lib/db';
import {
  HipaaScanR<PERSON>ult,
  HipaaCheckResult,
  LegacyHipaaCheckResult,
  LegacyHipaaScanResult,
  HipaaSeverity,
  HipaaCheckCategory,
} from '../types';

// Database and Knex type definitions
interface DatabaseFinding {
  check_id: string;
  description: string;
  passed: boolean;
  details: string | null;
}

interface KnexQueryBuilder {
  select(columns: string): KnexQueryBuilder;
  from(table: string): KnexQueryBuilder;
  whereRaw(sql: string): KnexQueryBuilder;
  where(column: string, value: unknown): KnexQueryBuilder;
}

/**
 * Legacy adapter for backward compatibility with existing scan service
 * Converts between new enhanced HIPAA results and legacy format
 */
export class LegacyHipaaAdapter {
  /**
   * Converts enhanced HIPAA scan result to legacy format
   * Maintains compatibility with existing scan service expectations
   */
  static toLegacyFormat(enhancedResult: HipaaScanResult): LegacyHipaaScanResult {
    const legacyChecks: LegacyHipaaCheckResult[] = enhancedResult.checks.map((check) => ({
      checkId: check.checkId,
      name: check.name,
      passed: check.passed,
      description: check.description,
      details: {
        // Flatten the enhanced details for legacy compatibility
        info: check.details.summary,
        target: enhancedResult.targetUrl,
        foundPrivacyPolicy: check.passed,
        // Include additional legacy-compatible fields
        ...check.details,
      },
      evidence: check.evidence?.[0]?.content || undefined,
    }));

    return {
      targetUrl: enhancedResult.targetUrl,
      timestamp: enhancedResult.timestamp,
      overallPassed: enhancedResult.overallPassed,
      checks: legacyChecks,
    };
  }

  /**
   * Converts legacy HIPAA check result to enhanced format
   * Upgrades legacy data to work with new 3-level analysis system
   */
  static fromLegacyFormat(legacyResult: LegacyHipaaScanResult): HipaaScanResult {
    const enhancedChecks: HipaaCheckResult[] = legacyResult.checks.map((check) => ({
      checkId: check.checkId,
      name: check.name,
      category: HipaaCheckCategory.PRESENCE, // Default category for legacy checks
      passed: check.passed,
      severity: check.passed ? HipaaSeverity.INFO : HipaaSeverity.HIGH,
      confidence: 95, // High confidence for basic checks
      description: check.description || '',
      details: {
        summary:
          typeof check.details === 'string'
            ? check.details
            : (check.details?.info || '').toString(),
        findings: [],
        metrics: {
          processingTime: 0,
          contentLength: 0,
          sectionsFound: 0,
          patternsMatched: check.passed ? 1 : 0,
          entitiesExtracted: 0,
        },
        context: {
          url: legacyResult.targetUrl,
        },
      },
      remediation: {
        priority: check.passed ? 'low' : 'high',
        effort: 'minimal',
        steps: check.passed ? ['No action needed'] : ['Add privacy policy to website'],
        resources: [],
        timeline: check.passed ? 'N/A' : '1-2 weeks',
      },
      metadata: {
        checkVersion: '1.0', // Legacy version
        processingTime: 0,
        analysisLevels: [1], // Only basic analysis for legacy
      },
      evidence: check.evidence
        ? [
            {
              type: 'text_excerpt',
              content: check.evidence.toString(),
              location: 'webpage',
              timestamp: new Date().toISOString(),
              relevance: 90,
            },
          ]
        : undefined,
    }));

    // Calculate summary from legacy data
    const passedChecks = enhancedChecks.filter((c) => c.passed).length;
    const failedChecks = enhancedChecks.filter((c) => !c.passed).length;
    const criticalIssues = enhancedChecks.filter(
      (c) => !c.passed && c.severity === 'critical',
    ).length;
    const highIssues = enhancedChecks.filter((c) => !c.passed && c.severity === 'high').length;

    return {
      targetUrl: legacyResult.targetUrl,
      timestamp: legacyResult.timestamp,
      overallScore: legacyResult.overallPassed ? 100 : 50,
      overallPassed: legacyResult.overallPassed,
      summary: {
        totalChecks: enhancedChecks.length,
        passedChecks,
        failedChecks,
        criticalIssues,
        highIssues,
        mediumIssues: 0,
        lowIssues: 0,
        overallScore: legacyResult.overallPassed ? 100 : 50,
        complianceLevel: legacyResult.overallPassed ? 'compliant' : 'non_compliant',
        analysisLevelsUsed: [1],
      },
      checks: enhancedChecks,
      recommendations: [],
      metadata: {
        version: '1.0',
        processingTime: 0,
        checksPerformed: enhancedChecks.length,
        analysisLevelsUsed: [1],
        cacheHits: 0,
        errors: [],
        warnings: [],
        userAgent: 'Legacy System',
        scanOptions: {},
      },
    };
  }

  /**
   * Saves legacy format result to both old and new database structures
   * Ensures backward compatibility while populating new enhanced tables
   */
  static async saveLegacyResult(
    scanId: string,
    legacyResult: LegacyHipaaScanResult,
  ): Promise<void> {
    const trx = await knex.transaction();

    try {
      // Save to legacy compliance_findings table for backward compatibility
      for (const check of legacyResult.checks) {
        await trx('compliance_findings').insert({
          scan_id: scanId,
          standard: 'hipaa',
          check_id: check.checkId,
          description: check.description || check.name,
          passed: check.passed,
          severity: check.passed ? 'info' : 'high',
          details: JSON.stringify(check.details),
          remediation_suggestion: check.passed
            ? 'No action needed'
            : 'Add comprehensive privacy policy to website',
        });
      }

      // Convert to enhanced format and save to new tables
      const enhancedResult = this.fromLegacyFormat(legacyResult);

      // Import and use the enhanced database functions
      const { HipaaDatabase } = await import('./hipaa-privacy-db');
      await HipaaDatabase.saveScanResult(scanId, enhancedResult);

      await trx.commit();
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  /**
   * Retrieves scan results in legacy format for backward compatibility
   */
  static async getLegacyResult(scanId: string): Promise<LegacyHipaaScanResult | null> {
    // Try to get from new enhanced tables first
    try {
      const { HipaaDatabase } = await import('./hipaa-privacy-db');
      const enhancedResult = await HipaaDatabase.getScanResult(scanId);

      if (enhancedResult) {
        return this.toLegacyFormat(enhancedResult);
      }
    } catch (error) {
      console.warn('Failed to retrieve from enhanced tables, falling back to legacy:', error);
    }

    // Fallback to legacy compliance_findings table
    const scan = await knex('scans').where('id', scanId).first();
    if (!scan) {
      return null;
    }

    const findings = await knex('compliance_findings')
      .where('scan_id', scanId)
      .where('standard', 'hipaa');

    const legacyChecks: LegacyHipaaCheckResult[] = findings.map((finding: DatabaseFinding) => ({
      checkId: finding.check_id,
      name: finding.description,
      passed: finding.passed,
      description: finding.description,
      details: finding.details ? JSON.parse(finding.details) : {},
      evidence: undefined,
    }));

    return {
      targetUrl: scan.url,
      timestamp: scan.created_at.toISOString(),
      overallPassed: legacyChecks.every((check) => check.passed),
      checks: legacyChecks,
    };
  }

  /**
   * Migrates existing legacy data to enhanced format
   * Useful for upgrading existing scan results
   */
  static async migrateLegacyData(batchSize = 100): Promise<number> {
    let migratedCount = 0;
    let offset = 0;

    // eslint-disable-next-line no-constant-condition
    while (true) {
      // Get batch of legacy scans
      const legacyScans = await knex('scans')
        .whereExists(function (this: KnexQueryBuilder) {
          this.select('*')
            .from('compliance_findings')
            .whereRaw('compliance_findings.scan_id = scans.id')
            .where('standard', 'hipaa');
        })
        .whereNotExists(function (this: KnexQueryBuilder) {
          this.select('*').from('hipaa_scans').whereRaw('hipaa_scans.scan_id = scans.id');
        })
        .limit(batchSize)
        .offset(offset);

      if (legacyScans.length === 0) {
        break; // No more scans to migrate
      }

      for (const scan of legacyScans) {
        try {
          const legacyResult = await this.getLegacyResult(scan.id);
          if (legacyResult) {
            const enhancedResult = this.fromLegacyFormat(legacyResult);
            const { HipaaDatabase } = await import('./hipaa-privacy-db');
            await HipaaDatabase.saveScanResult(scan.id, enhancedResult);
            migratedCount++;
          }
        } catch (error) {
          console.error(`Failed to migrate scan ${scan.id}:`, error);
        }
      }

      offset += batchSize;
    }

    return migratedCount;
  }

  /**
   * Validates that legacy and enhanced results are consistent
   * Useful for testing and verification
   */
  static validateConsistency(
    legacyResult: LegacyHipaaScanResult,
    enhancedResult: HipaaScanResult,
  ): boolean {
    // Check basic properties
    if (
      legacyResult.targetUrl !== enhancedResult.targetUrl ||
      legacyResult.overallPassed !== enhancedResult.overallPassed ||
      legacyResult.checks.length !== enhancedResult.checks.length
    ) {
      return false;
    }

    // Check each check result
    for (let i = 0; i < legacyResult.checks.length; i++) {
      const legacyCheck = legacyResult.checks[i];
      const enhancedCheck = enhancedResult.checks[i];

      if (
        legacyCheck.checkId !== enhancedCheck.checkId ||
        legacyCheck.passed !== enhancedCheck.passed
      ) {
        return false;
      }
    }

    return true;
  }
}
