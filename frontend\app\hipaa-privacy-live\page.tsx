'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Checkbox } from '@/components/ui/Checkbox';

import {
  Loader2,
  Shield,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Play,
  Settings,
  FileText,
  Users,
  Scale
} from 'lucide-react';
import { HipaaPrivacyResultsPage } from '@/components/hipaa-privacy';
import { HipaaPrivacyScanResult, HipaaPrivacyScanConfig } from '@/types/hipaa-privacy';

interface PrivacyScanConfig {
  targetUrl: string;
  timeout: number;
  maxRedirects: number;
  userAgent: string;
  includeSubdomains: boolean;
  enableLevel1: boolean;
  enableLevel2: boolean;
  enableLevel3: boolean;
  cacheResults: boolean;
  generateReport: boolean;
}

export default function HipaaPrivacyLivePage() {
  const [scanConfig, setScanConfig] = useState<PrivacyScanConfig>({
    targetUrl: '',
    timeout: 300000, // 5 minutes
    maxRedirects: 5,
    userAgent: 'HIPAA-Privacy-Scanner/1.0',
    includeSubdomains: false,
    enableLevel1: true,
    enableLevel2: true,
    enableLevel3: false,
    cacheResults: true,
    generateReport: true,
  });

  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [scanResult, setScanResult] = useState<HipaaPrivacyScanResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleStartScan = async () => {
    if (!scanConfig.targetUrl) {
      setError('Please enter a valid URL');
      return;
    }

    setIsScanning(true);
    setScanProgress(0);
    setError(null);
    setScanResult(null);

    try {
      // Simulate scan progress
      const progressInterval = setInterval(() => {
        setScanProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + Math.random() * 10;
        });
      }, 1000);

      // TODO: Replace with actual API call
      // const result = await hipaaPrivacyApi.startScan(scanConfig);
      
      // Simulate scan completion after 5 seconds
      setTimeout(() => {
        clearInterval(progressInterval);
        setScanProgress(100);
        
        // Mock result for demonstration
        const mockResult: HipaaPrivacyScanResult = {
          scanId: 'privacy-' + Date.now(),
          targetUrl: scanConfig.targetUrl,
          scanTimestamp: new Date(),
          scanDuration: 5000,
          overallScore: 78,
          overallPassed: false,
          summary: {
            totalChecks: 12,
            passedChecks: 9,
            failedChecks: 3,
            criticalIssues: 1,
            highIssues: 1,
            mediumIssues: 1,
            lowIssues: 0,
            compliancePercentage: 75,
            riskLevel: 'medium'
          },
          checks: [],
          recommendations: [],
          metadata: {
            scanVersion: '1.0.0',
            toolsUsed: ['Pattern Matcher', 'NLP Analyzer'],
            analysisLevels: scanConfig.enableLevel3 ? ['Level 1', 'Level 2', 'Level 3'] : ['Level 1', 'Level 2'],
            pagesAnalyzed: [scanConfig.targetUrl],
            totalContentLength: 15420,
            processingTime: 4800,
          }
        };
        
        setScanResult(mockResult);
        setIsScanning(false);
      }, 5000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during scanning');
      setIsScanning(false);
      setScanProgress(0);
    }
  };

  const handleExportReport = () => {
    // TODO: Implement export functionality
    console.log('Exporting report...');
  };

  const handleStartNewScan = () => {
    setScanResult(null);
    setScanProgress(0);
    setError(null);
  };

  if (scanResult) {
    return (
      <HipaaPrivacyResultsPage
        scanResult={scanResult}
        onExportReport={handleExportReport}
        onStartNewScan={handleStartNewScan}
      />
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <Shield className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">
              HIPAA Privacy Policy Compliance Scanner
            </h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Comprehensive analysis of your website's privacy policy for HIPAA compliance.
            Our multi-level analysis ensures thorough coverage of all requirements.
          </p>
        </div>

        {/* Main Scan Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Privacy Policy Scan Configuration
            </CardTitle>
            <CardDescription>
              Enter your website URL to begin a comprehensive HIPAA privacy policy compliance analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* URL Input */}
            <div className="space-y-2">
              <Label htmlFor="targetUrl">Website URL</Label>
              <Input
                id="targetUrl"
                type="url"
                placeholder="https://example.com"
                value={scanConfig.targetUrl}
                onChange={(e) => setScanConfig(prev => ({ ...prev, targetUrl: e.target.value }))}
                className="text-lg"
              />
            </div>

            {/* Analysis Levels */}
            <div className="space-y-4">
              <Label className="text-base font-semibold">Analysis Levels</Label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="level1"
                    checked={scanConfig.enableLevel1}
                    onCheckedChange={(checked) => 
                      setScanConfig(prev => ({ ...prev, enableLevel1: checked as boolean }))
                    }
                  />
                  <div className="grid gap-1.5 leading-none">
                    <Label htmlFor="level1" className="text-sm font-medium">
                      Level 1: Pattern Matching
                    </Label>
                    <p className="text-xs text-gray-600">
                      Basic keyword and phrase detection
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="level2"
                    checked={scanConfig.enableLevel2}
                    onCheckedChange={(checked) => 
                      setScanConfig(prev => ({ ...prev, enableLevel2: checked as boolean }))
                    }
                  />
                  <div className="grid gap-1.5 leading-none">
                    <Label htmlFor="level2" className="text-sm font-medium">
                      Level 2: NLP Analysis
                    </Label>
                    <p className="text-xs text-gray-600">
                      Natural language processing
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="level3"
                    checked={scanConfig.enableLevel3}
                    onCheckedChange={(checked) => 
                      setScanConfig(prev => ({ ...prev, enableLevel3: checked as boolean }))
                    }
                  />
                  <div className="grid gap-1.5 leading-none">
                    <Label htmlFor="level3" className="text-sm font-medium">
                      Level 3: AI Analysis
                    </Label>
                    <p className="text-xs text-gray-600">
                      Advanced AI-powered analysis
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Advanced Settings Toggle */}
            <div className="flex items-center justify-between pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                Advanced Settings
              </Button>
              
              <Button
                onClick={handleStartScan}
                disabled={isScanning || !scanConfig.targetUrl}
                className="flex items-center gap-2 px-8"
              >
                {isScanning ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
                {isScanning ? 'Scanning...' : 'Start Privacy Scan'}
              </Button>
            </div>

            {/* Advanced Settings */}
            {showAdvanced && (
              <div className="space-y-4 pt-4 border-t bg-gray-50 p-4 rounded">
                <h3 className="font-semibold text-gray-900">Advanced Configuration</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="timeout">Timeout (seconds)</Label>
                    <div className="px-3">
                      <input
                        id="timeout"
                        type="range"
                        value={scanConfig.timeout / 1000}
                        onChange={(e) =>
                          setScanConfig(prev => ({ ...prev, timeout: parseInt(e.target.value) * 1000 }))
                        }
                        max={600}
                        min={30}
                        step={30}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>30s</span>
                        <span>{scanConfig.timeout / 1000}s</span>
                        <span>600s</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxRedirects">Max Redirects</Label>
                    <Input
                      id="maxRedirects"
                      type="number"
                      min="0"
                      max="10"
                      value={scanConfig.maxRedirects}
                      onChange={(e) => setScanConfig(prev => ({ 
                        ...prev, 
                        maxRedirects: parseInt(e.target.value) || 0 
                      }))}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeSubdomains"
                      checked={scanConfig.includeSubdomains}
                      onCheckedChange={(checked) => 
                        setScanConfig(prev => ({ ...prev, includeSubdomains: checked as boolean }))
                      }
                    />
                    <Label htmlFor="includeSubdomains">Include subdomains</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="cacheResults"
                      checked={scanConfig.cacheResults}
                      onCheckedChange={(checked) => 
                        setScanConfig(prev => ({ ...prev, cacheResults: checked as boolean }))
                      }
                    />
                    <Label htmlFor="cacheResults">Cache results for faster re-scans</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="generateReport"
                      checked={scanConfig.generateReport}
                      onCheckedChange={(checked) => 
                        setScanConfig(prev => ({ ...prev, generateReport: checked as boolean }))
                      }
                    />
                    <Label htmlFor="generateReport">Generate detailed report</Label>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Scanning Progress */}
        {isScanning && (
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                  <span className="font-medium">Analyzing privacy policy compliance...</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(scanProgress)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${scanProgress}%` }}
                    />
                  </div>
                </div>
                <p className="text-sm text-gray-600">
                  This may take a few minutes depending on the complexity of your privacy policy.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error Display */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-800">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">Error</span>
              </div>
              <p className="text-red-700 mt-2">{error}</p>
            </CardContent>
          </Card>
        )}

        {/* Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <FileText className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <h3 className="font-semibold">Content Analysis</h3>
              <p className="text-sm text-gray-600">
                Comprehensive analysis of privacy policy content and structure
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Users className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <h3 className="font-semibold">Accessibility Check</h3>
              <p className="text-sm text-gray-600">
                Ensures privacy policy is accessible and easy to find
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Scale className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <h3 className="font-semibold">HIPAA Compliance</h3>
              <p className="text-sm text-gray-600">
                Validates compliance with HIPAA privacy requirements
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
