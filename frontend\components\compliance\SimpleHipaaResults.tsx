'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { CheckCircle, XCircle, Search, Brain, Zap, Clock } from 'lucide-react';
// Import types will be added as needed

/**
 * Simple HIPAA Results Component
 * Designed to work with the actual backend data structure from backend_logs.md
 */

interface AnalysisLevel {
  level: number;
  method: string;
  score: string; // Backend returns as string like "67.00"
  confidence: string; // Backend returns as string like "95.00"
  processingTime: number;
  foundPatterns?: number;
  totalPatterns?: number;
  entities?: Record<string, unknown>;
  privacyStatements?: Record<string, unknown>[];
  rightsStatements?: Record<string, unknown>[];
  identifiedGaps?: Record<string, unknown>[];
  riskFactors?: Record<string, unknown>[];
  recommendations?: Record<string, unknown>[];
  findings?: Record<string, unknown>[];
}

interface HipaaCheck {
  checkId: string;
  name: string;
  passed: boolean;
  score: string | null; // Backend returns as string like "57.00" or null
  confidence: string; // Backend returns as string like "92.00"
  analysisLevels: AnalysisLevel[];
}

interface SimpleHipaaResultsProps {
  enhancedHipaaResults: {
    targetUrl: string;
    overallScore: string; // Backend returns as string like "62.00"
    overallPassed: boolean;
    summary: {
      totalChecks: number;
      passedChecks: number;
      failedChecks: number;
      complianceLevel: string;
    };
    checksBreakdown: HipaaCheck[];
    recommendationsCount: number;
    metadata: {
      processingTime: number;
      version: string;
    };
  };
}

const SimpleHipaaResults: React.FC<SimpleHipaaResultsProps> = ({ enhancedHipaaResults }) => {
  // Parse scores from string format
  const parseScore = (scoreStr: string | null): number => {
    if (!scoreStr) return 0;
    const parsed = parseFloat(scoreStr);
    return isNaN(parsed) ? 0 : parsed;
  };

  const overallScore = parseScore(enhancedHipaaResults.overallScore);

  // Find the comprehensive check that has analysis levels with null safety
  const checksBreakdown =
    enhancedHipaaResults?.checksBreakdown ||
    ((enhancedHipaaResults as Record<string, unknown>)?.checks as HipaaCheck[]) ||
    [];

  const comprehensiveCheck = checksBreakdown.find(
    (check) =>
      check.checkId === 'HIPAA-COMPREHENSIVE-001' &&
      check.analysisLevels &&
      check.analysisLevels.length > 0,
  );

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getLevelIcon = (level: number) => {
    switch (level) {
      case 1:
        return <Search className="h-5 w-5" />;
      case 2:
        return <Brain className="h-5 w-5" />;
      case 3:
        return <Zap className="h-5 w-5" />;
      default:
        return <CheckCircle className="h-5 w-5" />;
    }
  };

  const getLevelTitle = (level: number) => {
    switch (level) {
      case 1:
        return 'Level 1: Pattern Matching';
      case 2:
        return 'Level 2: NLP Analysis';
      case 3:
        return 'Level 3: AI Analysis';
      default:
        return `Level ${level}`;
    }
  };

  // If we don't have the expected data structure, show debug info
  if (!enhancedHipaaResults) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Data Available</CardTitle>
        </CardHeader>
        <CardContent>
          <p>No enhanced HIPAA results data was provided.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overall Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {enhancedHipaaResults.overallPassed ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <XCircle className="h-5 w-5 text-red-600" />
            )}
            Enhanced HIPAA Analysis Results
          </CardTitle>
          <CardDescription>
            Comprehensive 3-level analysis for {enhancedHipaaResults.targetUrl}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className={`text-3xl font-bold ${getScoreColor(overallScore)}`}>
                {overallScore}%
              </div>
              <div className="text-sm text-muted-foreground">Overall Score</div>
              <Progress value={overallScore} className="mt-2" />
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {enhancedHipaaResults?.summary?.passedChecks || 0}/
                {enhancedHipaaResults?.summary?.totalChecks || 0}
              </div>
              <div className="text-sm text-muted-foreground">Checks Passed</div>
            </div>

            <div className="text-center">
              <div className="text-lg font-bold">
                <Badge variant={enhancedHipaaResults?.overallPassed ? 'default' : 'destructive'}>
                  {(enhancedHipaaResults?.summary?.complianceLevel || 'unknown')
                    .replace('_', ' ')
                    .toUpperCase()}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground">Compliance Level</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Analysis Levels - Only show if we have the comprehensive check */}
      {comprehensiveCheck && (
        <Card>
          <CardHeader>
            <CardTitle>3-Level Analysis Breakdown</CardTitle>
            <CardDescription>
              Detailed analysis using pattern matching, NLP, and AI techniques
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {comprehensiveCheck.analysisLevels.map((level) => {
                const score = parseScore(level.score);
                const confidence = parseScore(level.confidence);

                return (
                  <div key={level.level} className="border rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-3">
                      {getLevelIcon(level.level)}
                      <h3 className="text-lg font-semibold">{getLevelTitle(level.level)}</h3>
                      <Badge variant="outline" className="ml-auto">
                        <Clock className="h-3 w-3 mr-1" />
                        {level.processingTime}ms
                      </Badge>
                    </div>

                    <div className="mb-3">
                      <div className="text-sm text-muted-foreground mb-1">
                        Method: {level.method}
                      </div>
                      <div className="flex items-center gap-4">
                        <div className={`text-2xl font-bold ${getScoreColor(score)}`}>{score}%</div>
                        <Progress value={score} className="flex-1" />
                        <div className="text-sm text-muted-foreground">
                          {confidence}% confidence
                        </div>
                      </div>
                    </div>

                    {/* Level-specific details */}
                    {level.level === 1 && level.foundPatterns !== undefined && (
                      <div className="text-sm text-muted-foreground">
                        Found {level.foundPatterns} patterns out of {level.totalPatterns} total
                        patterns
                      </div>
                    )}

                    {level.level === 2 && (
                      <div className="text-sm text-muted-foreground">
                        NLP analysis with entity extraction and context understanding
                      </div>
                    )}

                    {level.level === 3 && (
                      <div className="text-sm text-muted-foreground">
                        AI-powered compliance assessment using DistilBERT
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Checks Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Checks Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {checksBreakdown.map((check) => (
              <div
                key={check.checkId}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  {check.passed ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                  <div>
                    <div className="font-medium">{check.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {check.checkId} • {parseScore(check.confidence)}% confidence
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  {check.score && (
                    <div className={`font-bold ${getScoreColor(parseScore(check.score))}`}>
                      {parseScore(check.score)}%
                    </div>
                  )}
                  <Badge variant={check.passed ? 'default' : 'destructive'}>
                    {check.passed ? 'PASSED' : 'FAILED'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Metadata */}
      <Card>
        <CardHeader>
          <CardTitle>Analysis Metadata</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium">Processing Time</div>
              <div className="text-muted-foreground">
                {enhancedHipaaResults?.metadata?.processingTime || 0}ms
              </div>
            </div>
            <div>
              <div className="font-medium">Analysis Version</div>
              <div className="text-muted-foreground">
                {enhancedHipaaResults?.metadata?.version || 'Unknown'}
              </div>
            </div>
            <div>
              <div className="font-medium">Total Checks</div>
              <div className="text-muted-foreground">
                {enhancedHipaaResults?.summary?.totalChecks || 0}
              </div>
            </div>
            <div>
              <div className="font-medium">Recommendations</div>
              <div className="text-muted-foreground">
                {enhancedHipaaResults?.recommendationsCount || 0}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SimpleHipaaResults;
