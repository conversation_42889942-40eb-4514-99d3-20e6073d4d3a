// Frontend types for HIPAA Privacy (matching backend types)

// Core HIPAA Privacy Types
export interface HipaaPrivacyScanResult {
  scanId: string;
  targetUrl: string;
  scanTimestamp: Date;
  scanDuration: number;
  overallScore: number;
  overallPassed: boolean;
  
  summary: HipaaPrivacyScanSummary;
  checks: HipaaPrivacyCheckResult[];
  recommendations: HipaaPrivacyRecommendation[];
  metadata: HipaaPrivacyScanMetadata;
}

export interface HipaaPrivacyScanSummary {
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  criticalIssues: number;
  highIssues: number;
  mediumIssues: number;
  lowIssues: number;
  compliancePercentage: number;
  riskLevel: HipaaPrivacyRiskLevel;
}

export interface HipaaPrivacyCheckResult {
  checkId: string;
  checkName: string;
  category: HipaaPrivacyCheckCategory;
  severity: HipaaPrivacySeverity;
  passed: boolean;
  score: number;
  description: string;
  findings: HipaaPrivacyFinding[];
  recommendations: string[];
  hipaaReference?: string;
  evidence?: HipaaPrivacyEvidence[];
}

export interface HipaaPrivacyFinding {
  type: HipaaPrivacyFindingType;
  severity: HipaaPrivacySeverity;
  message: string;
  location?: string;
  context?: string;
  evidence?: HipaaPrivacyEvidence;
  recommendation?: string;
}

export interface HipaaPrivacyEvidence {
  type: 'text' | 'html' | 'url' | 'pattern';
  content: string;
  location: string;
  context?: string;
  lineNumber?: number;
  confidence?: number;
}

export interface HipaaPrivacyRecommendation {
  id: string;
  title: string;
  description: string;
  priority: HipaaPrivacyPriority;
  effort: HipaaPrivacyEffort;
  impact: HipaaPrivacyImpact;
  category: HipaaPrivacyCheckCategory;
  actionItems: string[];
  resources?: string[];
  timeline?: string;
}

export interface HipaaPrivacyScanMetadata {
  scanVersion: string;
  toolsUsed: string[];
  analysisLevels: string[];
  pagesAnalyzed: string[];
  totalContentLength: number;
  processingTime: number;
  errors?: string[];
  warnings?: string[];
}

// Enums and Union Types
export type HipaaPrivacyRiskLevel = 'critical' | 'high' | 'medium' | 'low';
export type HipaaPrivacySeverity = 'critical' | 'high' | 'medium' | 'low' | 'info';
export type HipaaPrivacyPriority = 'critical' | 'high' | 'medium' | 'low';
export type HipaaPrivacyEffort = 'minimal' | 'moderate' | 'significant' | 'extensive';
export type HipaaPrivacyImpact = 'high' | 'medium' | 'low';
export type HipaaPrivacyScanStatus = 'pending' | 'running' | 'completed' | 'failed';

export enum HipaaPrivacyCheckCategory {
  PRESENCE = 'presence',
  ACCESSIBILITY = 'accessibility',
  CONTENT_STRUCTURE = 'content_structure',
  HIPAA_SPECIFIC = 'hipaa_specific',
  QUALITY = 'quality',
  LEGAL_COMPLIANCE = 'legal_compliance',
  CONTACT_INFO = 'contact_info',
}

export enum HipaaPrivacyFindingType {
  MISSING_CONTENT = 'missing_content',
  INCOMPLETE_CONTENT = 'incomplete_content',
  INCORRECT_FORMAT = 'incorrect_format',
  ACCESSIBILITY_ISSUE = 'accessibility_issue',
  ACCESSIBILITY_PASS = 'accessibility_pass',
  QUALITY_ISSUE = 'quality_issue',
  EXACT_MATCH = 'exact_match',
  PARTIAL_MATCH = 'partial_match',
  CONTEXT_MATCH = 'context_match',
  CONCEPT_MATCH = 'concept_match',
  ENTITY_FOUND = 'entity_found',
  CONCEPT_IDENTIFIED = 'concept_identified',
  COMPLIANCE_GAP = 'compliance_gap',
  RISK_FACTOR = 'risk_factor',
  LEGAL_ISSUE = 'legal_issue',
  READABILITY_ISSUE = 'readability_issue',
  ERROR = 'error',
}

// UI-specific types
export interface HipaaPrivacyResultsPageProps {
  scanResult: HipaaPrivacyScanResult;
  onExportReport?: () => void;
  onStartNewScan?: () => void;
}

export interface CheckResultCardProps {
  check: HipaaPrivacyCheckResult;
  expanded?: boolean;
  onToggleExpanded?: () => void;
}

export interface RecommendationCardProps {
  recommendation: HipaaPrivacyRecommendation;
  expanded?: boolean;
  onToggleExpanded?: () => void;
}

export interface ComplianceSummaryProps {
  summary: HipaaPrivacyScanSummary;
  scanResult: HipaaPrivacyScanResult;
}

export interface SectionBreakdownProps {
  checks: HipaaPrivacyCheckResult[];
  scanResult: HipaaPrivacyScanResult;
}

export interface ContentAnalysisProps {
  findings: HipaaPrivacyFinding[];
  evidence: HipaaPrivacyEvidence[];
}

// Scan configuration types
export interface HipaaPrivacyScanConfig {
  targetUrl: string;
  timeout?: number;
  maxRedirects?: number;
  userAgent?: string;
  includeSubdomains?: boolean;
  enableLevel1?: boolean; // Basic phrase matching
  enableLevel2?: boolean; // NLP analysis
  enableLevel3?: boolean; // AI analysis
  cacheResults?: boolean;
  generateReport?: boolean;
}
