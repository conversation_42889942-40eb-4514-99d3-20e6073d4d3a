@echo off
echo 🔧 Setting up Automatic WSL2 Issue Prevention
echo =============================================
echo This will create a Windows Task to prevent WSL2 issues automatically.

REM Check if running as Administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo ✅ Running as Administrator

set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..

echo Creating Windows Task Scheduler entry...

REM Create XML for task scheduler
(
echo ^<?xml version="1.0" encoding="UTF-16"?^>
echo ^<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task"^>
echo   ^<RegistrationInfo^>
echo     ^<Description^>Prevents WSL2 network issues for Docker Desktop^</Description^>
echo     ^<Author^>Comply Checker Project^</Author^>
echo   ^</RegistrationInfo^>
echo   ^<Triggers^>
echo     ^<BootTrigger^>
echo       ^<Enabled^>true^</Enabled^>
echo       ^<Delay^>PT2M^</Delay^>
echo     ^</BootTrigger^>
echo   ^</Triggers^>
echo   ^<Principals^>
echo     ^<Principal id="Author"^>
echo       ^<UserId^>S-1-5-18^</UserId^>
echo       ^<RunLevel^>HighestAvailable^</RunLevel^>
echo     ^</Principal^>
echo   ^</Principals^>
echo   ^<Settings^>
echo     ^<MultipleInstancesPolicy^>IgnoreNew^</MultipleInstancesPolicy^>
echo     ^<DisallowStartIfOnBatteries^>false^</DisallowStartIfOnBatteries^>
echo     ^<StopIfGoingOnBatteries^>false^</StopIfGoingOnBatteries^>
echo     ^<AllowHardTerminate^>true^</AllowHardTerminate^>
echo     ^<StartWhenAvailable^>true^</StartWhenAvailable^>
echo     ^<RunOnlyIfNetworkAvailable^>false^</RunOnlyIfNetworkAvailable^>
echo     ^<IdleSettings^>
echo       ^<StopOnIdleEnd^>false^</StopOnIdleEnd^>
echo       ^<RestartOnIdle^>false^</RestartOnIdle^>
echo     ^</IdleSettings^>
echo     ^<AllowStartOnDemand^>true^</AllowStartOnDemand^>
echo     ^<Enabled^>true^</Enabled^>
echo     ^<Hidden^>false^</Hidden^>
echo     ^<RunOnlyIfIdle^>false^</RunOnlyIfIdle^>
echo     ^<WakeToRun^>false^</WakeToRun^>
echo     ^<ExecutionTimeLimit^>PT5M^</ExecutionTimeLimit^>
echo     ^<Priority^>7^</Priority^>
echo   ^</Settings^>
echo   ^<Actions Context="Author"^>
echo     ^<Exec^>
echo       ^<Command^>cmd.exe^</Command^>
echo       ^<Arguments^>/c "cd /d "%PROJECT_DIR%" && scripts\docker-startup-guardian.bat"^</Arguments^>
echo       ^<WorkingDirectory^>%PROJECT_DIR%^</WorkingDirectory^>
echo     ^</Exec^>
echo   ^</Actions^>
echo ^</Task^>
) > "%TEMP%\WSL2Guardian.xml"

REM Import the task
schtasks /create /tn "WSL2 Docker Guardian" /xml "%TEMP%\WSL2Guardian.xml" /f >nul 2>&1

if %errorlevel% equ 0 (
    echo ✅ Automatic prevention task created successfully
    echo    Task Name: "WSL2 Docker Guardian"
    echo    Trigger: System startup (2 minute delay)
    echo    Action: Ensures WSL2 stability before Docker use
) else (
    echo ❌ Failed to create scheduled task
)

REM Clean up
del "%TEMP%\WSL2Guardian.xml" >nul 2>&1

echo.
echo 🛡️ Additional Prevention Measures:
echo.

echo Creating startup registry entries...
REM Ensure services start in correct order
reg add "HKLM\SYSTEM\CurrentControlSet\Services\LxssManager" /v DependOnService /t REG_MULTI_SZ /d "lanmanserver\0lanmanworkstation" /f >nul 2>&1

echo ✅ Service dependencies configured

echo Creating WSL configuration backup...
if exist "%USERPROFILE%\.wslconfig" (
    copy "%USERPROFILE%\.wslconfig" "%PROJECT_DIR%\wslconfig.backup" >nul 2>&1
    echo ✅ WSL config backed up
)

echo.
echo 🎯 Prevention Setup Complete!
echo.
echo The following automatic prevention measures are now active:
echo.
echo ✅ Windows Task Scheduler entry created
echo   • Runs on system startup
echo   • Ensures WSL2 stability
echo   • Prevents network share issues
echo.
echo ✅ Service dependencies configured
echo   • LxssManager waits for network services
echo   • Proper startup order enforced
echo.
echo ✅ Configuration backed up
echo   • WSL config saved to project directory
echo   • Can be restored if needed
echo.
echo 💡 To manually run prevention check:
echo    scripts\docker-startup-guardian.bat
echo.
echo 🗑️ To remove automatic prevention:
echo    schtasks /delete /tn "WSL2 Docker Guardian" /f
echo.
pause
