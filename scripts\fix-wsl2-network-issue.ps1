#!/usr/bin/env pwsh
# PowerShell script to fix WSL2 network issue for Docker Desktop

Write-Host "🔧 WSL2 Network Issue Fix for Docker Desktop" -ForegroundColor Red
Write-Host "============================================" -ForegroundColor Red
Write-Host ""
Write-Host "Error: 'The network name cannot be found' (\\wsl`$\docker-desktop)" -ForegroundColor Yellow
Write-Host "This WSL2 networking issue prevents Docker Desktop from accessing its distributions." -ForegroundColor Yellow
Write-Host ""

# Function to check if running as Administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to stop Docker processes
function Stop-DockerProcesses {
    Write-Host "🛑 Stopping Docker Desktop..." -ForegroundColor Yellow
    
    try {
        Get-Process -Name "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
        Stop-Service -Name "Docker Desktop Service" -Force -ErrorAction SilentlyContinue
        Write-Host "  ✅ Docker Desktop stopped" -ForegroundColor Green
    } catch {
        Write-Host "  ⚠️ Docker Desktop was not running" -ForegroundColor Gray
    }
}

# Function to restart WSL
function Restart-WSL {
    Write-Host "🔄 Restarting WSL completely..." -ForegroundColor Yellow
    
    # Shutdown WSL
    wsl --shutdown
    Start-Sleep -Seconds 5
    
    # Restart WSL service
    try {
        Stop-Service -Name "LxssManager" -Force -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 3
        Start-Service -Name "LxssManager" -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 5
        Write-Host "  ✅ WSL service restarted" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ Failed to restart WSL service" -ForegroundColor Red
    }
}

# Function to check WSL distributions
function Get-WSLDistributions {
    Write-Host "🔍 Checking WSL distributions..." -ForegroundColor Yellow
    
    try {
        $distributions = wsl --list --verbose 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Current WSL distributions:" -ForegroundColor White
            $distributions | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
            return $true
        } else {
            Write-Host "  ❌ Failed to list WSL distributions" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "  ❌ WSL command failed" -ForegroundColor Red
        return $false
    }
}

# Function to unregister Docker WSL distributions
function Remove-DockerWSLDistributions {
    Write-Host "🗑️ Removing Docker WSL distributions..." -ForegroundColor Yellow
    
    $distributions = @("docker-desktop", "docker-desktop-data")
    
    foreach ($distro in $distributions) {
        try {
            wsl --unregister $distro 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✅ Unregistered $distro" -ForegroundColor Green
            } else {
                Write-Host "  ⚠️ $distro was not registered" -ForegroundColor Gray
            }
        } catch {
            Write-Host "  ⚠️ Failed to unregister $distro" -ForegroundColor Gray
        }
    }
}

# Function to reset network
function Reset-NetworkStack {
    Write-Host "🌐 Resetting network stack..." -ForegroundColor Yellow
    
    try {
        ipconfig /flushdns | Out-Null
        netsh winsock reset | Out-Null
        netsh int ip reset | Out-Null
        Write-Host "  ✅ Network stack reset" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ Failed to reset network stack" -ForegroundColor Red
    }
}

# Function to restart network services
function Restart-NetworkServices {
    Write-Host "🔄 Restarting network services..." -ForegroundColor Yellow
    
    $services = @(
        "Winmgmt",
        "NlaSvc"  # Network Location Awareness
    )
    
    foreach ($service in $services) {
        try {
            Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 2
            Start-Service -Name $service -ErrorAction SilentlyContinue
            Write-Host "  ✅ Restarted $service" -ForegroundColor Green
        } catch {
            Write-Host "  ⚠️ Failed to restart $service" -ForegroundColor Yellow
        }
    }
}

# Function to update WSL2
function Update-WSL2 {
    Write-Host "📦 Updating WSL2..." -ForegroundColor Yellow
    
    try {
        wsl --update 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ WSL2 updated successfully" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️ WSL2 update not needed or failed" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "  ❌ Failed to update WSL2" -ForegroundColor Red
    }
    
    # Set WSL2 as default
    try {
        wsl --set-default-version 2 2>$null
        Write-Host "  ✅ WSL2 set as default version" -ForegroundColor Green
    } catch {
        Write-Host "  ⚠️ Failed to set WSL2 as default" -ForegroundColor Yellow
    }
}

# Function to test WSL network access
function Test-WSLNetworkAccess {
    Write-Host "🔍 Testing WSL network access..." -ForegroundColor Yellow
    
    try {
        $wslPath = "\\wsl$"
        if (Test-Path $wslPath) {
            Write-Host "  ✅ WSL network share (\\wsl`$) is accessible" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ⚠️ WSL network share not accessible yet" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "  ❌ Failed to test WSL network access" -ForegroundColor Red
        return $false
    }
}

# Function to find and start Docker Desktop
function Start-DockerDesktop {
    Write-Host "🚀 Starting Docker Desktop..." -ForegroundColor Yellow
    
    $dockerPaths = @(
        "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe",
        "${env:ProgramFiles(x86)}\Docker\Docker\Docker Desktop.exe",
        "${env:LOCALAPPDATA}\Programs\Docker\Docker\Docker Desktop.exe"
    )
    
    $dockerPath = $null
    foreach ($path in $dockerPaths) {
        if (Test-Path $path) {
            $dockerPath = $path
            break
        }
    }
    
    if (-not $dockerPath) {
        Write-Host "  ❌ Docker Desktop executable not found" -ForegroundColor Red
        return $false
    }
    
    Write-Host "  ✅ Found Docker Desktop at: $dockerPath" -ForegroundColor Green
    
    try {
        Start-Process -FilePath $dockerPath -WindowStyle Normal
        Write-Host "  ✅ Docker Desktop started" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "  ❌ Failed to start Docker Desktop: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to wait for Docker to be ready
function Wait-ForDockerReady {
    Write-Host "⏳ Waiting for Docker Desktop to initialize WSL distributions..." -ForegroundColor Yellow
    Write-Host "This process will:" -ForegroundColor White
    Write-Host "  • Create new docker-desktop WSL distribution" -ForegroundColor Gray
    Write-Host "  • Create new docker-desktop-data WSL distribution" -ForegroundColor Gray
    Write-Host "  • Set up WSL network share (\\wsl`$\docker-desktop)" -ForegroundColor Gray
    Write-Host "  • Initialize Docker daemon" -ForegroundColor Gray
    Write-Host ""
    Write-Host "This may take 3-5 minutes. Please be patient..." -ForegroundColor Cyan
    
    $maxAttempts = 20
    $attempt = 0
    
    while ($attempt -lt $maxAttempts) {
        $attempt++
        Write-Host ""
        Write-Host "Attempt $attempt/$maxAttempts : Checking Docker and WSL status..." -ForegroundColor Gray
        
        # Check if Docker WSL distributions exist
        try {
            $wslList = wsl --list --quiet 2>$null
            $hasDockerDesktop = $wslList -match "docker-desktop"
            
            if ($hasDockerDesktop) {
                Write-Host "  ✅ Docker WSL distributions detected" -ForegroundColor Green
                
                # Check if Docker daemon is responding
                try {
                    docker version --format "{{.Server.Version}}" 2>$null | Out-Null
                    if ($LASTEXITCODE -eq 0) {
                        Write-Host "  ✅ Docker daemon is responding" -ForegroundColor Green
                        return $true
                    } else {
                        Write-Host "  ⏳ Docker daemon still starting..." -ForegroundColor Yellow
                    }
                } catch {
                    Write-Host "  ⏳ Docker daemon still starting..." -ForegroundColor Yellow
                }
            } else {
                Write-Host "  ⏳ Docker WSL distributions still being created..." -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  ⏳ WSL still initializing..." -ForegroundColor Yellow
        }
        
        if ($attempt -eq $maxAttempts) {
            Write-Host "  ⚠️ Docker Desktop taking longer than expected" -ForegroundColor Yellow
            return $false
        }
        
        Start-Sleep -Seconds 15
    }
    
    return $false
}

# Main execution
if (-not (Test-Administrator)) {
    Write-Host "❌ This script must be run as Administrator" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Starting WSL2 network issue fix..." -ForegroundColor Cyan

# Step 1: Stop Docker
Stop-DockerProcesses

# Step 2: Restart WSL
Restart-WSL

# Step 3: Check current WSL distributions
Get-WSLDistributions

# Step 4: Remove Docker WSL distributions
Remove-DockerWSLDistributions

# Step 5: Reset network
Reset-NetworkStack

# Step 6: Restart network services
Restart-NetworkServices

# Step 7: Update WSL2
Update-WSL2

# Step 8: Complete WSL restart
Write-Host "🔄 Final WSL restart..." -ForegroundColor Yellow
wsl --shutdown
Start-Sleep -Seconds 10
Stop-Service -Name "LxssManager" -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 3
Start-Service -Name "LxssManager" -ErrorAction SilentlyContinue
Start-Sleep -Seconds 5

# Step 9: Test WSL network access
Test-WSLNetworkAccess

# Step 10: Start Docker Desktop
if (-not (Start-DockerDesktop)) {
    Write-Host "❌ Failed to start Docker Desktop" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 11: Wait for Docker to be ready
$dockerReady = Wait-ForDockerReady

# Final verification
Write-Host ""
Write-Host "📊 Final verification..." -ForegroundColor Cyan

if ($dockerReady) {
    Write-Host "🎉 WSL2 network issue fixed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Final status:" -ForegroundColor White
    
    # Show WSL distributions
    Write-Host "WSL distributions:" -ForegroundColor Cyan
    wsl --list --verbose
    
    # Show Docker status
    Write-Host "`nDocker status:" -ForegroundColor Cyan
    try {
        docker --version
        docker ps
    } catch {
        Write-Host "Docker commands still initializing..." -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "✅ Docker Desktop is now running with properly configured WSL2" -ForegroundColor Green
} else {
    Write-Host "⚠️ Docker Desktop may still be initializing" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📋 Manual checks:" -ForegroundColor Cyan
    Write-Host "1. Check Docker Desktop system tray icon" -ForegroundColor White
    Write-Host "2. Look for error messages in Docker Desktop" -ForegroundColor White
    Write-Host "3. Check WSL distributions: wsl --list --verbose" -ForegroundColor White
    Write-Host "4. Try accessing \\wsl`$\docker-desktop in File Explorer" -ForegroundColor White
    Write-Host ""
    Write-Host "If issues persist:" -ForegroundColor Cyan
    Write-Host "• Restart your computer" -ForegroundColor White
    Write-Host "• Update WSL2 kernel: https://aka.ms/wsl2kernel" -ForegroundColor White
    Write-Host "• Reinstall Docker Desktop" -ForegroundColor White
}

Write-Host ""
Write-Host "🔧 Additional WSL2 troubleshooting commands:" -ForegroundColor Cyan
Write-Host "wsl --status          # Check WSL status" -ForegroundColor Gray
Write-Host "wsl --list --verbose  # List distributions" -ForegroundColor Gray
Write-Host "wsl --update          # Update WSL" -ForegroundColor Gray
Write-Host "wsl --shutdown        # Shutdown WSL" -ForegroundColor Gray

Read-Host "`nPress Enter to continue"
