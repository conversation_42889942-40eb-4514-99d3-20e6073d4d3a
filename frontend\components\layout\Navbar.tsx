'use client';

import React from 'react';
import { useAuth } from '../../context/AuthContext';
import Link from 'next/link';

const Navbar: React.FC = () => {
  const { authenticated, login, logout, profile } = useAuth();

  // Authentication state tracking

  return (
    <nav className="bg-gray-800 text-white p-4 shadow-md w-full">
      <div className="container mx-auto flex justify-between items-center">
        <Link href="/" className="text-xl font-bold hover:text-gray-300">
          Comply Checker
        </Link>
        <div>
          {!authenticated ? (
            <button
              onClick={() => login()}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Login
            </button>
          ) : (
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard/scan/new"
                className="text-sm hover:text-gray-300 px-3 py-2 rounded-md bg-green-600 hover:bg-green-700"
              >
                New Scan
              </Link>
              <Link
                href="/dashboard/scans"
                className="text-sm hover:text-gray-300 px-3 py-2 rounded-md bg-blue-600 hover:bg-blue-700"
              >
                My Scans
              </Link>
              <Link
                href="/guidance"
                className="text-sm hover:text-gray-300 px-3 py-2 rounded-md bg-purple-600 hover:bg-purple-700"
              >
                Guidance
              </Link>
              <span className="text-sm">
                Welcome, {profile?.firstName || profile?.username || profile?.email || 'User'}
              </span>
              <button
                onClick={() => logout()}
                className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
              >
                Logout
              </button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
