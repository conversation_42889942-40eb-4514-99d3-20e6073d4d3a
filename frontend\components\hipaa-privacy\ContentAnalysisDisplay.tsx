import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/Collapsible';
import { 
  ChevronDown, 
  ChevronRight,
  Search,
  FileText,
  AlertTriangle,
  CheckCircle,
  Eye,
  Code,
  MapPin
} from 'lucide-react';
import { 
  HipaaPrivacyFinding,
  HipaaPrivacyEvidence,
  ContentAnalysisProps,
  HipaaPrivacyFindingType,
  HipaaPrivacySeverity
} from '@/types/hipaa-privacy';

export const ContentAnalysisDisplay: React.FC<ContentAnalysisProps> = ({
  findings,
  evidence,
}) => {
  const [expandedFindings, setExpandedFindings] = useState<Set<number>>(new Set());
  const [activeTab, setActiveTab] = useState('findings');

  const toggleFindingExpanded = (index: number) => {
    const newExpanded = new Set(expandedFindings);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedFindings(newExpanded);
  };

  const getSeverityColor = (severity: HipaaPrivacySeverity): string => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500 text-white';
      case 'high':
        return 'bg-orange-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-black';
      case 'low':
        return 'bg-blue-500 text-white';
      case 'info':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getFindingTypeColor = (type: HipaaPrivacyFindingType): string => {
    switch (type) {
      case HipaaPrivacyFindingType.MISSING_CONTENT:
      case HipaaPrivacyFindingType.COMPLIANCE_GAP:
        return 'bg-red-100 text-red-800';
      case HipaaPrivacyFindingType.INCOMPLETE_CONTENT:
      case HipaaPrivacyFindingType.QUALITY_ISSUE:
        return 'bg-orange-100 text-orange-800';
      case HipaaPrivacyFindingType.PARTIAL_MATCH:
      case HipaaPrivacyFindingType.CONTEXT_MATCH:
        return 'bg-yellow-100 text-yellow-800';
      case HipaaPrivacyFindingType.EXACT_MATCH:
      case HipaaPrivacyFindingType.CONCEPT_MATCH:
        return 'bg-green-100 text-green-800';
      case HipaaPrivacyFindingType.ACCESSIBILITY_PASS:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getEvidenceTypeIcon = (type: string) => {
    switch (type) {
      case 'text':
        return <FileText className="h-4 w-4" />;
      case 'html':
        return <Code className="h-4 w-4" />;
      case 'url':
        return <MapPin className="h-4 w-4" />;
      case 'pattern':
        return <Search className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  // Group findings by severity
  const findingsBySeverity = findings.reduce((acc, finding, index) => {
    if (!acc[finding.severity]) {
      acc[finding.severity] = [];
    }
    acc[finding.severity].push({ ...finding, originalIndex: index });
    return acc;
  }, {} as Record<HipaaPrivacySeverity, Array<HipaaPrivacyFinding & { originalIndex: number }>>);

  // Group evidence by type
  const evidenceByType = evidence.reduce((acc, evidenceItem, index) => {
    if (!acc[evidenceItem.type]) {
      acc[evidenceItem.type] = [];
    }
    acc[evidenceItem.type].push({ ...evidenceItem, originalIndex: index });
    return acc;
  }, {} as Record<string, Array<HipaaPrivacyEvidence & { originalIndex: number }>>);

  const renderFinding = (finding: HipaaPrivacyFinding & { originalIndex: number }) => {
    const isExpanded = expandedFindings.has(finding.originalIndex);
    
    return (
      <Card key={finding.originalIndex} className="overflow-hidden">
        <Collapsible open={isExpanded} onOpenChange={() => toggleFindingExpanded(finding.originalIndex)}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  <div className="flex-1">
                    <CardTitle className="text-base">{finding.message}</CardTitle>
                    {finding.location && (
                      <p className="text-sm text-gray-600 mt-1">
                        Location: {finding.location}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getSeverityColor(finding.severity)}>
                    {finding.severity.toUpperCase()}
                  </Badge>
                  <Badge variant="outline" className={getFindingTypeColor(finding.type)}>
                    {finding.type.replace('_', ' ').toUpperCase()}
                  </Badge>
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          
          <CollapsibleContent>
            <CardContent className="pt-0">
              <div className="space-y-4">
                {/* Context */}
                {finding.context && (
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Context</h4>
                    <div className="p-3 bg-gray-50 rounded text-sm">
                      <pre className="whitespace-pre-wrap">{finding.context}</pre>
                    </div>
                  </div>
                )}

                {/* Evidence */}
                {finding.evidence && (
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Evidence</h4>
                    <div className="p-3 bg-blue-50 rounded">
                      <div className="flex items-center gap-2 mb-2">
                        {getEvidenceTypeIcon(finding.evidence.type)}
                        <Badge variant="outline" className="text-xs">
                          {finding.evidence.type.toUpperCase()}
                        </Badge>
                        <span className="text-xs text-gray-600">{finding.evidence.location}</span>
                        {finding.evidence.confidence && (
                          <span className="text-xs text-gray-600">
                            Confidence: {Math.round(finding.evidence.confidence * 100)}%
                          </span>
                        )}
                      </div>
                      <pre className="whitespace-pre-wrap text-sm text-gray-700">
                        {finding.evidence.content}
                      </pre>
                    </div>
                  </div>
                )}

                {/* Recommendation */}
                {finding.recommendation && (
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Recommendation</h4>
                    <div className="p-3 bg-green-50 rounded text-sm text-green-800">
                      {finding.recommendation}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    );
  };

  const renderEvidence = (evidenceItem: HipaaPrivacyEvidence & { originalIndex: number }) => (
    <Card key={evidenceItem.originalIndex}>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            {getEvidenceTypeIcon(evidenceItem.type)}
            <Badge variant="outline" className="text-xs">
              {evidenceItem.type.toUpperCase()}
            </Badge>
            <span className="text-sm text-gray-600">{evidenceItem.location}</span>
            {evidenceItem.confidence && (
              <Badge variant="outline" className="text-xs">
                {Math.round(evidenceItem.confidence * 100)}% confidence
              </Badge>
            )}
          </div>
          
          <div className="p-3 bg-gray-50 rounded">
            <pre className="whitespace-pre-wrap text-sm text-gray-700">
              {evidenceItem.content}
            </pre>
          </div>

          {evidenceItem.context && (
            <div>
              <span className="text-xs font-medium text-gray-600">Context:</span>
              <div className="mt-1 p-2 bg-blue-50 rounded">
                <pre className="whitespace-pre-wrap text-xs text-gray-600">
                  {evidenceItem.context}
                </pre>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  if (findings.length === 0 && evidence.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-lg font-medium">No content analysis data available</p>
        <p className="text-sm">Content analysis will appear here when available.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="findings">
            Findings ({findings.length})
          </TabsTrigger>
          <TabsTrigger value="evidence">
            Evidence ({evidence.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="findings" className="space-y-4">
          {Object.entries(findingsBySeverity).map(([severity, severityFindings]) => (
            <div key={severity}>
              <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Badge className={getSeverityColor(severity as HipaaPrivacySeverity)}>
                  {severity.toUpperCase()}
                </Badge>
                <span>({severityFindings.length})</span>
              </h3>
              <div className="space-y-3">
                {severityFindings.map(renderFinding)}
              </div>
            </div>
          ))}
        </TabsContent>

        <TabsContent value="evidence" className="space-y-4">
          {Object.entries(evidenceByType).map(([type, typeEvidence]) => (
            <div key={type}>
              <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                {getEvidenceTypeIcon(type)}
                <span>{type.toUpperCase()} Evidence ({typeEvidence.length})</span>
              </h3>
              <div className="space-y-3">
                {typeEvidence.map(renderEvidence)}
              </div>
            </div>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
};
