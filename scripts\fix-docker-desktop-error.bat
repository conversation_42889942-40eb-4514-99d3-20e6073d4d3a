@echo off
echo 🔧 Docker Desktop Error Fix Script
echo ==================================
echo.
echo Error: "running services: running com.docker.build: exit status 1"
echo This is a common Docker Desktop startup issue.
echo.

echo Step 1: Stopping Docker Desktop completely...
taskkill /F /IM "Docker Desktop.exe" >nul 2>&1
taskkill /F /IM "com.docker.cli.exe" >nul 2>&1
taskkill /F /IM "com.docker.backend.exe" >nul 2>&1
taskkill /F /IM "com.docker.proxy.exe" >nul 2>&1
taskkill /F /IM "dockerd.exe" >nul 2>&1
echo ✅ Docker processes stopped

echo Step 2: Stopping Docker services...
net stop "Docker Desktop Service" >nul 2>&1
net stop "com.docker.service" >nul 2>&1
echo ✅ Docker services stopped

echo Step 3: Clearing Docker Desktop data (this will reset settings)...
echo ⚠️ This will reset Docker Desktop to default settings
echo Press Ctrl+C to cancel, or
pause

echo Clearing Docker Desktop data...
if exist "%APPDATA%\Docker" (
    rmdir /S /Q "%APPDATA%\Docker" >nul 2>&1
    echo ✅ Cleared %APPDATA%\Docker
)

if exist "%LOCALAPPDATA%\Docker" (
    rmdir /S /Q "%LOCALAPPDATA%\Docker" >nul 2>&1
    echo ✅ Cleared %LOCALAPPDATA%\Docker
)

if exist "%PROGRAMDATA%\Docker" (
    rmdir /S /Q "%PROGRAMDATA%\Docker" >nul 2>&1
    echo ✅ Cleared %PROGRAMDATA%\Docker
)

if exist "%PROGRAMDATA%\DockerDesktop" (
    rmdir /S /Q "%PROGRAMDATA%\DockerDesktop" >nul 2>&1
    echo ✅ Cleared %PROGRAMDATA%\DockerDesktop
)

echo Step 4: Clearing Windows containers (if any)...
docker system prune -a -f >nul 2>&1

echo Step 5: Resetting Windows networking...
echo Resetting network adapters...
netsh winsock reset >nul 2>&1
netsh int ip reset >nul 2>&1
echo ✅ Network reset completed

echo Step 6: Checking Windows features...
echo Checking Hyper-V and Containers feature...
dism /online /get-featureinfo /featurename:Microsoft-Hyper-V >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Hyper-V feature available
) else (
    echo ⚠️ Hyper-V not available - will use WSL2
)

dism /online /get-featureinfo /featurename:Containers >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Containers feature available
) else (
    echo ❌ Containers feature not available
    echo Enabling Containers feature...
    dism /online /enable-feature /featurename:Containers /all /norestart
)

echo Step 7: Starting Docker Desktop with clean state...
echo Looking for Docker Desktop executable...

set DOCKER_PATH=""
if exist "%ProgramFiles%\Docker\Docker\Docker Desktop.exe" (
    set DOCKER_PATH="%ProgramFiles%\Docker\Docker\Docker Desktop.exe"
) else if exist "%ProgramFiles(x86)%\Docker\Docker\Docker Desktop.exe" (
    set DOCKER_PATH="%ProgramFiles(x86)%\Docker\Docker\Docker Desktop.exe"
) else if exist "%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe" (
    set DOCKER_PATH="%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe"
) else (
    echo ❌ Docker Desktop executable not found
    echo Please reinstall Docker Desktop
    pause
    exit /b 1
)

echo ✅ Found Docker Desktop at: %DOCKER_PATH%
echo Starting Docker Desktop...
start "" %DOCKER_PATH%

echo Step 8: Waiting for Docker Desktop to start...
echo This may take 2-5 minutes. Please be patient...
echo.
echo 👀 Watch for:
echo • Docker whale icon in system tray
echo • Icon should stop animating when ready
echo • Right-click icon to see status
echo.

set /a attempts=0
:wait_loop
set /a attempts+=1
echo Attempt %attempts%/24: Checking Docker daemon...

docker version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker Desktop started successfully!
    goto :success
)

if %attempts% geq 24 (
    echo ⚠️ Docker Desktop taking longer than expected
    goto :manual_check
)

timeout /t 15 /nobreak >nul
goto :wait_loop

:success
echo.
echo 🎉 Docker Desktop is now running!
echo.
echo Testing basic functionality...
docker --version
docker ps
echo.
echo ✅ Docker Desktop fix completed successfully!
goto :end

:manual_check
echo.
echo ⚠️ Docker Desktop may still be starting or has issues
echo.
echo 📋 Manual checks:
echo 1. Look at system tray - is Docker whale icon present?
echo 2. Right-click Docker icon - what does it say?
echo 3. If still showing errors, try the alternative fixes below
echo.

:end
echo.
echo 🔧 If Docker Desktop still won't start, try these alternatives:
echo.
echo Alternative 1: Reinstall Docker Desktop
echo • Uninstall Docker Desktop completely
echo • Restart computer
echo • Download fresh installer from docker.com
echo • Install as Administrator
echo.
echo Alternative 2: Switch to WSL2 backend
echo • In Docker Desktop settings
echo • Go to General tab
echo • Check "Use WSL 2 based engine"
echo • Apply and restart
echo.
echo Alternative 3: Check Windows version
echo • Docker requires Windows 10 version 2004+ or Windows 11
echo • Run: winver
echo • Update Windows if needed
echo.
echo Alternative 4: Enable virtualization in BIOS
echo • Restart computer
echo • Enter BIOS/UEFI settings
echo • Enable "Intel VT-x" or "AMD-V"
echo • Enable "VT-d" if available
echo • Save and restart
echo.
pause
