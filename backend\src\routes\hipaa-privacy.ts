import { Router, Request, Response, NextFunction } from 'express';
import { HipaaPrivacyPolicyOrchestrator } from '../compliance/hipaa/privacy/services/privacy-orchestrator';
import { InputValidator, AuditLogger } from '../config/security';
import { HipaaScanOptions } from '../compliance/hipaa/privacy/types';

// Import express-validator functions directly
const expressValidator = require('express-validator');
const { body, param, query, validationResult } = expressValidator;

const router = Router();
const orchestrator = new HipaaPrivacyPolicyOrchestrator();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Start HIPAA Privacy Scan
router.post(
  '/scan',
  [
    body('targetUrl')
      .isURL({ require_protocol: true, protocols: ['http', 'https'] })
      .withMessage('Valid URL is required'),
    body('timeout')
      .optional()
      .isInt({ min: 30000, max: 600000 })
      .withMessage('timeout must be between 30 seconds and 10 minutes'),
    body('maxRedirects')
      .optional()
      .isInt({ min: 0, max: 10 })
      .withMessage('maxRedirects must be between 0 and 10'),
    body('enableLevel1')
      .optional()
      .isBoolean()
      .withMessage('enableLevel1 must be a boolean'),
    body('enableLevel2')
      .optional()
      .isBoolean()
      .withMessage('enableLevel2 must be a boolean'),
    body('enableLevel3')
      .optional()
      .isBoolean()
      .withMessage('enableLevel3 must be a boolean'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const {
        targetUrl,
        timeout = 300000,
        maxRedirects = 5,
        userAgent = 'HIPAA-Privacy-Scanner/1.0',
        includeSubdomains = false,
        enableLevel1 = true,
        enableLevel2 = true,
        enableLevel3 = false,
        cacheResults = true,
        generateReport = true,
      } = req.body;

      // Validate URL security
      const urlValidation = InputValidator.validateUrl(targetUrl);
      if (!urlValidation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Invalid URL',
          message: urlValidation.reason,
        });
      }

      // Log privacy scan initiation
      console.log('🚀 HIPAA Privacy scan initiated via API');
      console.log('📋 Request details:', {
        targetUrl,
        timeout,
        maxRedirects,
        enableLevel1,
        enableLevel2,
        enableLevel3,
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
      });

      AuditLogger.logSecurityEvent(
        'hipaa_privacy_scan_started',
        {
          targetUrl,
          userAgent: req.get('User-Agent'),
          ip: req.ip,
        },
        req,
      );

      const scanOptions: HipaaScanOptions = {
        timeout,
        maxRedirects,
        userAgent,
        includeSubdomains,
        enableLevel1,
        enableLevel2,
        enableLevel3,
        cacheResults,
        generateReport,
      };

      console.log('🔍 Starting HIPAA Privacy scan with options:', scanOptions);

      const result = await orchestrator.performComprehensiveScan(targetUrl, scanOptions);

      console.log('✅ HIPAA Privacy scan completed successfully');
      console.log('📊 Scan results summary:', {
        overallScore: result.overallScore,
        overallPassed: result.overallPassed,
        totalChecks: result.checks.length,
        recommendations: result.recommendations.length,
      });

      AuditLogger.logSecurityEvent(
        'hipaa_privacy_scan_completed',
        {
          targetUrl,
          overallScore: result.overallScore,
          overallPassed: result.overallPassed,
          scanDuration: Date.now() - new Date(result.timestamp).getTime(),
        },
        req,
      );

      res.json({
        success: true,
        data: {
          scanId: `privacy-${Date.now()}`, // Generate a unique scan ID
          status: 'completed',
          message: 'HIPAA privacy scan completed successfully',
          result,
        },
      });
    } catch (error) {
      console.error('❌ HIPAA Privacy scan failed:', error);

      AuditLogger.logSecurityEvent(
        'hipaa_privacy_scan_failed',
        {
          targetUrl: req.body.targetUrl,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        req,
      );

      res.status(500).json({
        success: false,
        error: 'HIPAA privacy scan failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Get Scan Status (placeholder for future async implementation)
router.get(
  '/scan/:scanId/status',
  [param('scanId').notEmpty().withMessage('Scan ID is required')],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId } = req.params;

      // For now, return completed status since scans are synchronous
      res.json({
        success: true,
        data: {
          scanId,
          status: 'completed',
          progress: 100,
          message: 'Scan completed',
        },
      });
    } catch (error) {
      console.error('Get scan status error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get scan status',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Get Scan Result (placeholder for future database storage)
router.get(
  '/scan/:scanId/result',
  [param('scanId').notEmpty().withMessage('Scan ID is required')],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId } = req.params;

      // For now, return a message that results are returned immediately
      res.status(404).json({
        success: false,
        error: 'Scan result not found',
        message: 'Privacy scan results are returned immediately with the scan request. Use the /scan endpoint.',
      });
    } catch (error) {
      console.error('Get scan result error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get scan result',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Get All Scans (placeholder for future database storage)
router.get(
  '/scans',
  [
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('limit must be between 1 and 100'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      // For now, return empty array since we don't store scans yet
      res.json({
        success: true,
        data: [],
        metadata: {
          count: 0,
          limit: parseInt(req.query.limit as string) || 50,
        },
      });
    } catch (error) {
      console.error('Get all scans error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get scans',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Delete Scan (placeholder for future database storage)
router.delete(
  '/scan/:scanId',
  [param('scanId').notEmpty().withMessage('Scan ID is required')],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId } = req.params;

      res.status(404).json({
        success: false,
        error: 'Scan not found',
        message: 'Privacy scans are not stored persistently yet.',
      });
    } catch (error) {
      console.error('Delete scan error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete scan',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Export Scan Report
router.get(
  '/scan/:scanId/export',
  [
    param('scanId').notEmpty().withMessage('Scan ID is required'),
    query('format').optional().isIn(['pdf', 'json']).withMessage('format must be pdf or json'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId } = req.params;
      const format = (req.query.format as string) || 'pdf';

      res.status(501).json({
        success: false,
        error: 'Export not yet implemented',
        message: 'Privacy scan export functionality will be available in a future update.',
      });
    } catch (error) {
      console.error('Export scan error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to export scan',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Health check endpoint
router.get('/health', async (req: Request, res: Response) => {
  try {
    res.json({
      success: true,
      data: {
        service: 'HIPAA Privacy Scanner',
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      },
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      success: false,
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export default router;
