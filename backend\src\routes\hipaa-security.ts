import { Router, Request, Response, NextFunction } from 'express';
import { HipaaSecurityOrchestrator } from '../compliance/hipaa/security/hipaa-security-orchestrator';
import { InputValidator, AuditLogger } from '../config/security';
import { HipaaSecurityScanConfig } from '../compliance/hipaa/security/types';

// Import express-validator functions directly
const expressValidator = require('express-validator');
const { body, param, query, validationResult } = expressValidator;

const router = Router();
const orchestrator = new HipaaSecurityOrchestrator();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Start HIPAA Security Scan
router.post(
  '/scan',
  [
    body('targetUrl')
      .isURL({ require_protocol: true, protocols: ['http', 'https'] })
      .withMessage('Valid URL is required'),
    body('maxPages')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('maxPages must be between 1 and 50'),
    body('scanDepth')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('scanDepth must be between 1 and 5'),
    body('timeout')
      .optional()
      .isInt({ min: 60000, max: 3600000 })
      .withMessage('timeout must be between 1 minute and 1 hour'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const {
        targetUrl,
        maxPages,
        scanDepth,
        timeout,
        enableVulnerabilityScanning,
        enableSSLAnalysis,
        enableContentAnalysis,
      } = req.body;

      // Security validation
      if (!InputValidator.isValidUrl(targetUrl, { allowedDomains: [], blockedDomains: [] })) {
        return res.status(400).json({
          success: false,
          error: 'Invalid or potentially malicious URL',
        });
      }

      // Log security scan initiation
      console.log('🚀 HIPAA Security scan initiated via API');
      console.log('📋 Request details:', {
        targetUrl,
        maxPages: maxPages || 15,
        scanDepth: scanDepth || 2,
        timeout: timeout || 1800000,
        enableVulnerabilityScanning: enableVulnerabilityScanning !== false,
        enableSSLAnalysis: enableSSLAnalysis !== false,
        enableContentAnalysis: enableContentAnalysis !== false,
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
      });

      AuditLogger.logSecurityEvent(
        'hipaa_security_scan_started',
        {
          targetUrl,
          userAgent: req.get('User-Agent'),
          ip: req.ip,
        },
        req,
      );

      // Optimize settings for large government sites
      const isLargeSite = targetUrl.includes('healthcare.gov') || targetUrl.includes('gov');
      const optimizedMaxPages = isLargeSite ? Math.min(maxPages || 15, 25) : maxPages || 15;
      const optimizedDepth = isLargeSite ? Math.min(scanDepth || 2, 2) : scanDepth || 2;

      const config: Partial<HipaaSecurityScanConfig> = {
        targetUrl,
        maxPages: optimizedMaxPages,
        scanDepth: optimizedDepth,
        timeout: timeout || 1800000,
        enableVulnerabilityScanning: enableVulnerabilityScanning !== false,
        enableSSLAnalysis: enableSSLAnalysis !== false,
        enableContentAnalysis: enableContentAnalysis !== false,
      };

      console.log(
        `📋 Scan configuration optimized for ${isLargeSite ? 'large government site' : 'regular site'}:`,
        {
          maxPages: optimizedMaxPages,
          scanDepth: optimizedDepth,
        },
      );

      // Start comprehensive scan
      console.log('🔄 Starting comprehensive HIPAA security scan...');
      const startTime = Date.now();
      const result = await orchestrator.performComprehensiveScan(targetUrl, config);
      const scanDuration = Date.now() - startTime;

      console.log('✅ HIPAA Security scan completed successfully!');
      console.log('📊 Scan results summary:', {
        scanId: result.scanId,
        targetUrl: result.targetUrl,
        overallScore: result.overallScore,
        riskLevel: result.riskLevel,
        passedTests: result.passedTests.length,
        failedTests: result.failedTests.length,
        vulnerabilities: result.vulnerabilities.length,
        pagesScanned: result.pagesScanned.length,
        scanDuration: `${scanDuration}ms`,
        toolsUsed: result.toolsUsed,
      });

      res.json({
        success: true,
        data: {
          scanId: result.scanId,
          status: 'completed',
          message: 'HIPAA security scan completed successfully',
          result,
        },
      });
    } catch (error) {
      console.error('❌ HIPAA security scan failed:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        targetUrl: req.body.targetUrl,
        stack: error instanceof Error ? error.stack : undefined,
      });

      AuditLogger.logSecurityEvent(
        'hipaa_security_scan_error',
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          targetUrl: req.body.targetUrl,
        },
        req,
      );

      res.status(500).json({
        success: false,
        error: 'Failed to perform HIPAA security scan',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Get Scan Status
router.get(
  '/scan/:scanId/status',
  [param('scanId').isUUID().withMessage('Valid scan ID is required')],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId } = req.params;
      const result = await orchestrator.getScanResult(scanId);

      if (!result) {
        return res.status(404).json({
          success: false,
          error: 'Scan not found',
        });
      }

      res.json({
        success: true,
        data: {
          scanId,
          status: result.scanStatus,
          progress:
            result.scanStatus === 'completed' ? 100 : result.scanStatus === 'running' ? 50 : 0,
          message: result.errorMessage || 'Scan in progress',
          result: result.scanStatus === 'completed' ? result : undefined,
        },
      });
    } catch (error) {
      console.error('Get scan status error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get scan status',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Get Scan Result
router.get(
  '/scan/:scanId/result',
  [param('scanId').isUUID().withMessage('Valid scan ID is required')],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId } = req.params;
      const result = await orchestrator.getScanResult(scanId);

      if (!result) {
        return res.status(404).json({
          success: false,
          error: 'Scan result not found',
        });
      }

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error('Get scan result error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get scan result',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Get All Scans
router.get(
  '/scans',
  [
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('limit must be between 1 and 100'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const limit = parseInt(req.query.limit as string) || 50;
      const scans = await orchestrator.getAllScans(limit);

      res.json({
        success: true,
        data: scans,
        metadata: {
          count: scans.length,
          limit,
        },
      });
    } catch (error) {
      console.error('Get all scans error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get scans',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Delete Scan
router.delete(
  '/scan/:scanId',
  [param('scanId').isUUID().withMessage('Valid scan ID is required')],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId } = req.params;
      const deleted = await orchestrator.deleteScan(scanId);

      if (!deleted) {
        return res.status(404).json({
          success: false,
          error: 'Scan not found',
        });
      }

      AuditLogger.logSecurityEvent(
        'hipaa_security_scan_deleted',
        {
          scanId,
        },
        req,
      );

      res.json({
        success: true,
        message: 'Scan deleted successfully',
      });
    } catch (error) {
      console.error('Delete scan error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete scan',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Test ZAP Connectivity (Development/Debug endpoint)
router.get('/test-zap', async (req: Request, res: Response) => {
  try {
    console.log('🔍 Testing ZAP connectivity via API endpoint...');

    // Import the scanner config service
    const { ScannerConfigService } = await import(
      '../compliance/hipaa/security/services/scanner-config'
    );
    const configService = ScannerConfigService.getInstance();

    // Get configuration
    const config = configService.getScannerConfig();
    console.log('📋 Nuclei Configuration:', {
      nucleiPath: config.nucleiPath,
      nucleiTemplatesPath: config.nucleiTemplatesPath,
    });

    // Test connectivity
    const connectivityResult = await configService.testConnectivity();

    console.log('📊 Connectivity test results:', connectivityResult);

    res.json({
      success: true,
      data: {
        nucleiConfiguration: {
          nucleiPath: config.nucleiPath,
          nucleiTemplatesPath: config.nucleiTemplatesPath,
        },
        connectivityTest: connectivityResult,
        environmentVariables: {
          NUCLEI_PATH: process.env.NUCLEI_PATH,
          NUCLEI_TEMPLATES_PATH: process.env.NUCLEI_TEMPLATES_PATH,
          NODE_ENV: process.env.NODE_ENV,
          HIPAA_SECURITY_ENABLED: process.env.HIPAA_SECURITY_ENABLED,
        },
      },
    });
  } catch (error) {
    console.error('❌ Nuclei connectivity test failed:', error);
    res.status(500).json({
      success: false,
      error: 'Nuclei connectivity test failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Test Smart URL Discovery (Development/Debug endpoint)
router.post('/discover-urls', async (req: Request, res: Response) => {
  try {
    const { targetUrl, maxUrls = 25 } = req.body;

    if (!targetUrl) {
      return res.status(400).json({
        success: false,
        error: 'targetUrl is required',
      });
    }

    console.log(`🔍 Testing smart URL discovery for: ${targetUrl}`);

    // Import the security URL resolver
    const { SecurityURLResolver } = await import(
      '../compliance/hipaa/security/services/security-url-resolver'
    );

    // Test smart URL discovery
    const securityLinks = await SecurityURLResolver.findSecurityRelevantUrls(targetUrl, maxUrls);
    const urls = securityLinks.map((link) => link.url);

    console.log(`📊 Smart discovery found ${urls.length} security-relevant URLs`);

    res.json({
      success: true,
      data: {
        targetUrl,
        maxUrls,
        method: 'Smart Analysis (Footer/Header/Navigation)',
        urls,
        urlDetails: securityLinks,
        summary: {
          totalUrls: urls.length,
          byType: securityLinks.reduce(
            (acc, link) => {
              acc[link.type] = (acc[link.type] || 0) + 1;
              return acc;
            },
            {} as Record<string, number>,
          ),
          byPriority: securityLinks.reduce(
            (acc, link) => {
              acc[link.priority] = (acc[link.priority] || 0) + 1;
              return acc;
            },
            {} as Record<string, number>,
          ),
          byLocation: securityLinks.reduce(
            (acc, link) => {
              acc[link.location] = (acc[link.location] || 0) + 1;
              return acc;
            },
            {} as Record<string, number>,
          ),
        },
      },
    });
  } catch (error) {
    console.error('❌ Smart URL discovery failed:', error);
    res.status(500).json({
      success: false,
      error: 'Smart URL discovery failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Test All Internal Links Extraction (Development/Debug endpoint)
router.post('/extract-all-links', async (req: Request, res: Response) => {
  try {
    const { targetUrl, maxUrls = 50 } = req.body;

    if (!targetUrl) {
      return res.status(400).json({
        success: false,
        error: 'targetUrl is required',
      });
    }

    console.log(`🔍 Testing ALL internal links extraction for: ${targetUrl}`);

    // Import the security URL resolver
    const { SecurityURLResolver } = await import(
      '../compliance/hipaa/security/services/security-url-resolver'
    );

    // Test all internal links extraction
    const allUrls = await SecurityURLResolver.findAllInternalLinks(targetUrl, maxUrls);

    console.log(`📊 Extracted ${allUrls.length} internal URLs from homepage`);

    res.json({
      success: true,
      data: {
        targetUrl,
        maxUrls,
        method: 'All Internal Links Extraction (Homepage Analysis)',
        urls: allUrls,
        summary: {
          totalUrls: allUrls.length,
          samplePaths: allUrls.slice(0, 10).map((url) => {
            try {
              return new URL(url).pathname;
            } catch {
              return url;
            }
          }),
          urlTypes: {
            html: allUrls.filter((url) => !url.includes('.')).length,
            pdf: allUrls.filter((url) => url.includes('.pdf')).length,
            images: allUrls.filter((url) => /\.(jpg|jpeg|png|gif|svg)/.test(url)).length,
            other: allUrls.filter(
              (url) => url.includes('.') && !/\.(jpg|jpeg|png|gif|svg|pdf)/.test(url),
            ).length,
          },
        },
      },
    });
  } catch (error) {
    console.error('❌ All internal links extraction failed:', error);
    res.status(500).json({
      success: false,
      error: 'All internal links extraction failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Test Database Connectivity (Development/Debug endpoint)
router.get('/test-database', async (req: Request, res: Response) => {
  try {
    console.log('🔍 Testing database connectivity...');

    // Import the database class
    const { HipaaSecurityDatabase } = await import(
      '../compliance/hipaa/security/database/hipaa-security-database'
    );
    const database = new HipaaSecurityDatabase();

    const startTime = Date.now();

    // Test connection
    const connectionTest = await database.testConnection();
    const connectionTime = Date.now() - startTime;

    // Test table existence
    const tableTest = await database.checkTableExists();
    const totalTime = Date.now() - startTime;

    await database.close();

    res.json({
      success: true,
      data: {
        connectionTest: {
          success: connectionTest,
          duration: `${connectionTime}ms`,
        },
        tableTest: {
          success: tableTest,
          duration: `${totalTime - connectionTime}ms`,
        },
        totalDuration: `${totalTime}ms`,
        databaseUrl: process.env.DATABASE_URL?.replace(/:[^:@]*@/, ':***@') || 'Not set',
        environment: process.env.NODE_ENV,
      },
    });
  } catch (error) {
    console.error('❌ Database connectivity test failed:', error);
    res.status(500).json({
      success: false,
      error: 'Database connectivity test failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      details: {
        code: (error as { code?: string | number })?.code,
        errno: (error as { errno?: number })?.errno,
        syscall: (error as { syscall?: string })?.syscall,
      },
    });
  }
});

// Export Scan Report
router.get(
  '/scan/:scanId/export',
  [
    param('scanId').isUUID().withMessage('Valid scan ID is required'),
    query('format').optional().isIn(['pdf', 'json']).withMessage('format must be pdf or json'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId } = req.params;
      const format = (req.query.format as string) || 'pdf';

      const result = await orchestrator.getScanResult(scanId);
      if (!result) {
        return res.status(404).json({
          success: false,
          error: 'Scan result not found',
        });
      }

      if (format === 'json') {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader(
          'Content-Disposition',
          `attachment; filename="hipaa-security-scan-${scanId}.json"`,
        );
        res.json(result);
      } else {
        // PDF export would require additional implementation
        res.status(501).json({
          success: false,
          error: 'PDF export not yet implemented',
          message: 'Use JSON format for now',
        });
      }
    } catch (error) {
      console.error('Export scan error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to export scan',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Test Nuclei Availability (Development/Debug endpoint)
router.get('/test-nuclei-availability', async (req: Request, res: Response) => {
  try {
    console.log('🔍 Testing Nuclei availability via API endpoint...');

    // Import the Nuclei client
    const { NucleiClient } = await import('../compliance/hipaa/security/services/nuclei-client');
    const nucleiClient = new NucleiClient();

    // Test Nuclei availability
    console.log('🔍 Checking Nuclei availability...');
    const isAvailable = await nucleiClient.isAvailable();
    console.log('📋 Nuclei availability result:', isAvailable);

    res.json({
      success: true,
      message: 'Nuclei availability test completed',
      nucleiAvailable: isAvailable,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Failed to test Nuclei availability:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test Nuclei availability',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export default router;
