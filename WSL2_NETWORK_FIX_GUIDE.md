# 🔧 WSL2 Network Issue Fix Guide

## Error Description
```
deploying WSL2 distributions
ensuring main distro is deployed: checking if main distro is up to date: 
checking main distro bootstrap version: getting main distro bootstrap version: 
open \\wsl$\docker-desktop\etc\wsl_bootstrap_version: The network name cannot be found.
```

This error occurs when Docker Desktop cannot access its WSL2 distributions due to network share issues with `\\wsl$`.

## 🚀 Quick Fix (Automated)

### Method 1: Run the Fix Script
```cmd
cd "D:\Web projects\Comply Checker"
.\scripts\fix-wsl2-network-issue.bat
```

### Method 2: PowerShell Version (More Advanced)
```powershell
cd "D:\Web projects\Comply Checker"
.\scripts\fix-wsl2-network-issue.ps1
```

## 🔧 Manual Fix Steps

### Step 1: Stop Everything
```cmd
# Stop Docker Desktop
taskkill /F /IM "Docker Desktop.exe"
net stop "Docker Desktop Service"

# Shutdown WSL completely
wsl --shutdown
```

### Step 2: Restart WSL Service
```cmd
# Restart WSL service as Administrator
net stop LxssManager
net start LxssManager
```

### Step 3: Remove Docker WSL Distributions
```cmd
# This forces Docker to recreate them
wsl --unregister docker-desktop
wsl --unregister docker-desktop-data
```

### Step 4: Reset Network Stack
```cmd
# Clear network cache
ipconfig /flushdns
netsh winsock reset
netsh int ip reset
```

### Step 5: Update WSL2
```cmd
# Update WSL2 kernel and set as default
wsl --update
wsl --set-default-version 2
```

### Step 6: Complete WSL Restart
```cmd
# Full WSL restart
wsl --shutdown
net stop LxssManager
timeout /t 5
net start LxssManager
```

### Step 7: Start Docker Desktop
- Start Docker Desktop as Administrator
- Wait 3-5 minutes for WSL distributions to be recreated
- Check system tray for steady Docker whale icon

## 🔍 Verification Steps

### Check WSL Distributions
```cmd
wsl --list --verbose
```
Should show:
```
  NAME                   STATE           VERSION
* docker-desktop         Running         2
  docker-desktop-data    Running         2
```

### Check WSL Network Share
- Open File Explorer
- Navigate to `\\wsl$`
- Should see `docker-desktop` folder

### Test Docker
```cmd
docker --version
docker ps
```
Both should work without errors.

## 🛠️ Advanced Troubleshooting

### Issue 1: WSL Service Won't Start
```cmd
# Check Windows features
dism /online /get-featureinfo /featurename:Microsoft-Windows-Subsystem-Linux

# Enable if disabled
dism /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
```

### Issue 2: Network Share Still Not Working
```cmd
# Restart network services
net stop "Server"
net start "Server"
net stop "Workstation" 
net start "Workstation"

# Check SMB settings
sc config lanmanserver start= auto
sc start lanmanserver
```

### Issue 3: WSL2 Kernel Issues
1. Download latest WSL2 kernel: https://aka.ms/wsl2kernel
2. Install the update
3. Restart computer
4. Run: `wsl --update`

### Issue 4: Hyper-V Conflicts
```cmd
# Disable Hyper-V if using WSL2
bcdedit /set hypervisorlaunchtype off
# Restart computer
# Re-enable if needed later:
# bcdedit /set hypervisorlaunchtype auto
```

## 🔄 Alternative Solutions

### Solution 1: Reset WSL Completely
```cmd
# Backup any important WSL data first!
wsl --unregister Ubuntu  # or other distros you want to keep
wsl --unregister docker-desktop
wsl --unregister docker-desktop-data

# Reinstall WSL
wsl --install
```

### Solution 2: Switch Docker Backend
1. Open Docker Desktop Settings
2. Go to General tab
3. Try switching between WSL2 and Hyper-V backends
4. Apply & Restart

### Solution 3: Registry Fix (Advanced)
⚠️ **Backup registry first!**

1. Open `regedit` as Administrator
2. Navigate to: `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\LxssManager`
3. Check that `Start` value is `3` (Manual)
4. Navigate to: `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Lxss`
5. Delete any corrupted distribution entries

### Solution 4: Windows Update
- Update to latest Windows 10 (version 2004+) or Windows 11
- Install all pending updates
- Restart computer

## 🔍 Diagnostic Commands

```cmd
# Check WSL status
wsl --status

# Check WSL version
wsl --version

# List all distributions
wsl --list --all --verbose

# Check Windows version
winver

# Check network shares
net share

# Check services
sc query LxssManager
sc query lanmanserver

# Test network connectivity
ping 127.0.0.1
telnet localhost 445
```

## 📊 Expected Results After Fix

### WSL Status
```cmd
C:\> wsl --list --verbose
  NAME                   STATE           VERSION
* docker-desktop         Running         2
  docker-desktop-data    Running         2
```

### Network Share Access
- `\\wsl$` accessible in File Explorer
- `\\wsl$\docker-desktop` folder exists
- No "network name cannot be found" errors

### Docker Functionality
```cmd
C:\> docker --version
Docker version 24.0.7, build afdd53b

C:\> docker ps
CONTAINER ID   IMAGE     COMMAND   CREATED   STATUS    PORTS     NAMES
```

## 🆘 If Nothing Works

### Last Resort Options:

1. **Complete Docker Reinstall:**
   - Uninstall Docker Desktop
   - Remove all WSL distributions: `wsl --unregister [name]`
   - Clear Docker data folders
   - Restart computer
   - Reinstall Docker Desktop

2. **WSL Reset:**
   - Backup important data
   - Uninstall WSL: `dism /online /disable-feature /featurename:Microsoft-Windows-Subsystem-Linux`
   - Restart computer
   - Reinstall WSL: `wsl --install`

3. **System File Check:**
   ```cmd
   sfc /scannow
   dism /online /cleanup-image /restorehealth
   ```

4. **Windows Reset:**
   - If all else fails, consider Windows reset/reinstall
   - Backup all important data first

## ✅ Success Verification

Once fixed, test with our project:

```cmd
cd "D:\Web projects\Comply Checker"
docker-compose config
docker-compose up -d
docker-compose ps
```

Should show 4 running containers without errors:
- comply_checker_backend
- comply_checker_postgres  
- comply_checker_keycloak
- comply_checker_mailhog

## 📞 Additional Resources

- **WSL Documentation:** https://docs.microsoft.com/en-us/windows/wsl/
- **Docker Desktop WSL2:** https://docs.docker.com/desktop/wsl/
- **WSL2 Kernel Updates:** https://aka.ms/wsl2kernel
- **Windows Features:** https://docs.microsoft.com/en-us/windows/wsl/install

This WSL2 network issue is common but fixable. The automated scripts should resolve it in most cases.
