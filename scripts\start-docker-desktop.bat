@echo off
echo 🐳 Starting Docker Desktop...
echo ============================

echo 🔍 Checking if Docker Desktop is installed...

REM Check common installation paths
set DOCKER_PATH=""
if exist "%ProgramFiles%\Docker\Docker\Docker Desktop.exe" (
    set DOCKER_PATH="%ProgramFiles%\Docker\Docker\Docker Desktop.exe"
    goto :found
)
if exist "%ProgramFiles(x86)%\Docker\Docker\Docker Desktop.exe" (
    set DOCKER_PATH="%ProgramFiles(x86)%\Docker\Docker\Docker Desktop.exe"
    goto :found
)
if exist "%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe" (
    set DOCKER_PATH="%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe"
    goto :found
)

echo ❌ Docker Desktop not found!
echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop
pause
exit /b 1

:found
echo ✅ Docker Desktop found at: %DOCKER_PATH%

echo 🔍 Checking if Docker Desktop is already running...
tasklist /FI "IMAGENAME eq Docker Desktop.exe" 2>NUL | find /I /N "Docker Desktop.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Docker Desktop is already running
    goto :check_daemon
)

echo 🚀 Starting Docker Desktop...
start "" %DOCKER_PATH%
echo ✅ Docker Desktop started

echo ⏳ Waiting for Docker Desktop to initialize...
echo Please wait for the Docker whale icon in your system tray to stop animating
echo This may take 1-2 minutes for first-time startup...

:check_daemon
echo 🔍 Testing Docker daemon...

REM Wait a bit for Docker to start
timeout /t 10 /nobreak >nul

REM Test Docker daemon multiple times
set /a attempts=0
:test_loop
set /a attempts+=1
echo Attempt %attempts%/12: Testing Docker daemon...

docker version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker daemon is ready!
    goto :success
)

if %attempts% geq 12 (
    echo ⚠️ Docker daemon not responding after 2 minutes
    echo This might be normal for first-time startup
    goto :manual_check
)

timeout /t 10 /nobreak >nul
goto :test_loop

:success
echo 🎉 Docker Desktop is running successfully!
echo.
echo 📋 Docker Information:
docker --version
docker-compose --version
echo.
echo 🚀 You can now run your Docker services:
echo    cd "D:\Web projects\Comply Checker"
echo    docker-compose up -d
echo.
goto :end

:manual_check
echo.
echo 📋 Manual Check Required:
echo 1. Look at your system tray (bottom-right corner)
echo 2. Find the Docker whale icon
echo 3. If it's still animating, Docker is still starting
echo 4. If it's steady, try running: docker ps
echo 5. If Docker Desktop shows errors, restart it
echo.
echo 🔧 If Docker Desktop won't start:
echo • Try running as Administrator
echo • Restart your computer
echo • Check if virtualization is enabled in BIOS
echo • Check Windows Event Viewer for errors
echo.

:end
pause
