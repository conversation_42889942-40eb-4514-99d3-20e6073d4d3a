import { Router } from 'express';
import hipaaSecurityRoutes from '../hipaa-security';

const router = Router();

/**
 * @openapi
 * tags:
 *   name: HIPAA Compliance
 *   description: Routes for HIPAA specific compliance checks and operations.
 */

// Mount HIPAA Security routes under /security
router.use('/security', hipaaSecurityRoutes);

// Legacy HIPAA routes can be added here if needed
// Example:
// router.post('/check-access-controls', (req, res) => { ... });

export default router;
