import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import { Progress } from '@/components/ui/Progress';
import { AlertTriangle, CheckCircle, XCircle, Download, RefreshCw, FileText, Shield } from 'lucide-react';
import { 
  HipaaPrivacyScanResult, 
  HipaaPrivacyRiskLevel,
  HipaaPrivacyResultsPageProps 
} from '@/types/hipaa-privacy';
import { CheckResultsList } from './CheckResultsList';
import { RecommendationsList } from './RecommendationsList';
import { ComplianceSummary } from './ComplianceSummary';
import { SectionBreakdown } from './SectionBreakdown';
import { ContentAnalysisDisplay } from './ContentAnalysisDisplay';

export const HipaaPrivacyResultsPage: React.FC<HipaaPrivacyResultsPageProps> = ({
  scanResult,
  onExportReport,
  onStartNewScan,
}) => {
  const [activeTab, setActiveTab] = useState('summary');

  const getRiskLevelColor = (riskLevel: HipaaPrivacyRiskLevel): string => {
    switch (riskLevel) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getRiskLevelIcon = (riskLevel: HipaaPrivacyRiskLevel) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return <XCircle className="h-6 w-6 text-red-500" />;
      case 'medium':
        return <AlertTriangle className="h-6 w-6 text-yellow-500" />;
      case 'low':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      default:
        return <Shield className="h-6 w-6 text-gray-500" />;
    }
  };

  const failedChecks = scanResult.checks.filter(check => !check.passed);
  const passedChecks = scanResult.checks.filter(check => check.passed);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            HIPAA Privacy Policy Compliance Results
          </h1>
          <p className="text-gray-600">
            Scan completed for: <span className="font-medium">{scanResult.targetUrl}</span>
          </p>
          <p className="text-sm text-gray-500">
            Scanned on {new Date(scanResult.scanTimestamp).toLocaleString()} • 
            Duration: {Math.round(scanResult.scanDuration / 1000)}s
          </p>
        </div>
        <div className="flex gap-3">
          {onExportReport && (
            <Button onClick={onExportReport} variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Export Report
            </Button>
          )}
          {onStartNewScan && (
            <Button onClick={onStartNewScan} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              New Scan
            </Button>
          )}
        </div>
      </div>

      {/* Overall Score Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getRiskLevelIcon(scanResult.summary.riskLevel)}
            Overall HIPAA Privacy Compliance Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="flex justify-between items-center mb-3">
                <span className="text-3xl font-bold text-gray-900">{scanResult.overallScore}%</span>
                <Badge
                  className={`${getRiskLevelColor(scanResult.summary.riskLevel)} text-white font-semibold px-3 py-1`}
                >
                  {scanResult.summary.riskLevel.toUpperCase()} RISK
                </Badge>
              </div>
              <Progress value={scanResult.overallScore} className="h-4 mb-2" />
              <div className="text-sm text-gray-600 font-medium">Privacy Policy Compliance Score</div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-green-600">{scanResult.summary.passedChecks}</div>
              <div className="text-sm text-gray-500">Passed</div>
              <div className="text-2xl font-bold text-red-600 mt-2">{scanResult.summary.failedChecks}</div>
              <div className="text-sm text-gray-500">Failed</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Checks</p>
                <p className="text-2xl font-bold">{scanResult.summary.totalChecks}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical Issues</p>
                <p className="text-2xl font-bold text-red-600">{scanResult.summary.criticalIssues}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">High Issues</p>
                <p className="text-2xl font-bold text-orange-600">{scanResult.summary.highIssues}</p>
              </div>
              <XCircle className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Compliance %</p>
                <p className="text-2xl font-bold text-green-600">{scanResult.summary.compliancePercentage}%</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Results Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="summary">Summary</TabsTrigger>
          <TabsTrigger value="failed">Failed Checks</TabsTrigger>
          <TabsTrigger value="passed">Passed Checks</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="sections">HIPAA Sections</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-6">
          <ComplianceSummary summary={scanResult.summary} scanResult={scanResult} />
        </TabsContent>

        <TabsContent value="failed" className="space-y-6">
          <div className="flex items-center gap-3 mb-6">
            <XCircle className="h-6 w-6 text-red-500" />
            <h2 className="text-2xl font-bold text-gray-900">Failed Checks</h2>
            <Badge variant="destructive" className="text-sm font-semibold px-3 py-1">
              {failedChecks.length}
            </Badge>
          </div>
          {failedChecks.length > 0 ? (
            <CheckResultsList checks={failedChecks} showFailureDetails={true} />
          ) : (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <p className="text-lg font-medium">All checks passed!</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="passed" className="space-y-6">
          <div className="flex items-center gap-3 mb-6">
            <CheckCircle className="h-6 w-6 text-green-500" />
            <h2 className="text-2xl font-bold text-gray-900">Passed Checks</h2>
            <Badge variant="default" className="text-sm font-semibold px-3 py-1 bg-green-500">
              {passedChecks.length}
            </Badge>
          </div>
          <CheckResultsList checks={passedChecks} showFailureDetails={false} />
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <div className="flex items-center gap-3 mb-6">
            <AlertTriangle className="h-6 w-6 text-yellow-500" />
            <h2 className="text-2xl font-bold text-gray-900">Recommendations</h2>
            <Badge variant="outline" className="text-sm font-semibold px-3 py-1">
              {scanResult.recommendations.length}
            </Badge>
          </div>
          <RecommendationsList recommendations={scanResult.recommendations} />
        </TabsContent>

        <TabsContent value="sections" className="space-y-6">
          <div className="flex items-center gap-3 mb-6">
            <Shield className="h-6 w-6 text-blue-500" />
            <h2 className="text-2xl font-bold text-gray-900">HIPAA Section Breakdown</h2>
          </div>
          <SectionBreakdown checks={scanResult.checks} scanResult={scanResult} />
        </TabsContent>
      </Tabs>
    </div>
  );
};
