#!/usr/bin/env pwsh
# PowerShell script to start Docker Desktop and diagnose issues

Write-Host "🐳 Docker Desktop Startup and Diagnostic Script" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# Function to check if Docker Desktop is installed
function Find-DockerDesktop {
    $possiblePaths = @(
        "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe",
        "${env:ProgramFiles(x86)}\Docker\Docker\Docker Desktop.exe",
        "${env:LOCALAPPDATA}\Programs\Docker\Docker\Docker Desktop.exe",
        "${env:APPDATA}\Docker\Docker Desktop.exe"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    return $null
}

# Function to check if Docker Desktop process is running
function Test-DockerDesktopProcess {
    $processes = Get-Process -Name "Docker Desktop" -ErrorAction SilentlyContinue
    return $processes.Count -gt 0
}

# Function to check Docker daemon status
function Test-DockerDaemon {
    try {
        $result = docker version --format "{{.Server.Version}}" 2>$null
        return $LASTEXITCODE -eq 0
    }
    catch {
        return $false
    }
}

# Function to check Windows features required for Docker
function Test-WindowsFeatures {
    Write-Host "🔍 Checking Windows features required for Docker..." -ForegroundColor Yellow
    
    # Check if running on Windows 10/11
    $osVersion = [System.Environment]::OSVersion.Version
    Write-Host "   OS Version: $($osVersion.Major).$($osVersion.Minor)" -ForegroundColor White
    
    # Check Hyper-V (for Windows 10 Pro/Enterprise)
    try {
        $hyperV = Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All -ErrorAction SilentlyContinue
        if ($hyperV) {
            Write-Host "   Hyper-V Status: $($hyperV.State)" -ForegroundColor White
        } else {
            Write-Host "   Hyper-V: Not available (may be using WSL2)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   Hyper-V: Cannot check status" -ForegroundColor Yellow
    }
    
    # Check WSL2
    try {
        $wslVersion = wsl --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   WSL2: Available" -ForegroundColor Green
        } else {
            Write-Host "   WSL2: Not available or not configured" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   WSL2: Cannot check status" -ForegroundColor Yellow
    }
}

# Main execution
Write-Host "🔍 Step 1: Checking if Docker Desktop is installed..." -ForegroundColor Blue

$dockerPath = Find-DockerDesktop
if (-not $dockerPath) {
    Write-Host "❌ Docker Desktop not found!" -ForegroundColor Red
    Write-Host "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    Write-Host "After installation, restart your computer and run this script again." -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Docker Desktop found at: $dockerPath" -ForegroundColor Green

Write-Host "`n🔍 Step 2: Checking if Docker Desktop process is running..." -ForegroundColor Blue

if (Test-DockerDesktopProcess) {
    Write-Host "✅ Docker Desktop process is running" -ForegroundColor Green
} else {
    Write-Host "⚠️ Docker Desktop process not running. Starting it..." -ForegroundColor Yellow
    
    try {
        Start-Process -FilePath $dockerPath -WindowStyle Normal
        Write-Host "🚀 Docker Desktop started. Please wait for it to initialize..." -ForegroundColor Green
        Write-Host "   Look for the Docker whale icon in your system tray" -ForegroundColor White
        Write-Host "   Wait until it stops animating and shows 'Docker Desktop is running'" -ForegroundColor White
    } catch {
        Write-Host "❌ Failed to start Docker Desktop: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n🔍 Step 3: Waiting for Docker daemon to be ready..." -ForegroundColor Blue

$maxWaitTime = 120  # 2 minutes
$waitInterval = 5   # 5 seconds
$elapsed = 0

Write-Host "⏳ Waiting for Docker daemon (this may take up to 2 minutes)..." -ForegroundColor Yellow

while ($elapsed -lt $maxWaitTime) {
    if (Test-DockerDaemon) {
        Write-Host "✅ Docker daemon is ready!" -ForegroundColor Green
        break
    }
    
    Write-Host "." -NoNewline -ForegroundColor Yellow
    Start-Sleep -Seconds $waitInterval
    $elapsed += $waitInterval
}

Write-Host ""

if ($elapsed -ge $maxWaitTime) {
    Write-Host "⚠️ Docker daemon did not start within $maxWaitTime seconds" -ForegroundColor Yellow
    Write-Host "This might be normal for first-time startup. Please check:" -ForegroundColor White
    Write-Host "1. Docker Desktop system tray icon should be steady (not animated)" -ForegroundColor White
    Write-Host "2. Try running 'docker ps' manually in a few minutes" -ForegroundColor White
}

Write-Host "`n🔍 Step 4: Testing Docker functionality..." -ForegroundColor Blue

try {
    $version = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker CLI: $version" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker CLI not responding" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Docker CLI error: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    $composeVersion = docker-compose --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker Compose: $composeVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker Compose not responding" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Docker Compose error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test basic Docker functionality
Write-Host "`n🔍 Step 5: Testing basic Docker operations..." -ForegroundColor Blue

try {
    $containers = docker ps 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker daemon is responding to commands" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker daemon not responding to 'docker ps'" -ForegroundColor Red
        Write-Host "   This usually means Docker Desktop is still starting up" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Error running 'docker ps': $($_.Exception.Message)" -ForegroundColor Red
}

# Check Windows features
Test-WindowsFeatures

Write-Host "`n📋 Summary and Next Steps:" -ForegroundColor Cyan

if (Test-DockerDaemon) {
    Write-Host "🎉 Docker Desktop is running successfully!" -ForegroundColor Green
    Write-Host "`nYou can now run:" -ForegroundColor White
    Write-Host "   cd `"D:\Web projects\Comply Checker`"" -ForegroundColor Gray
    Write-Host "   docker-compose up -d" -ForegroundColor Gray
} else {
    Write-Host "⚠️ Docker Desktop may still be starting up." -ForegroundColor Yellow
    Write-Host "`nPlease:" -ForegroundColor White
    Write-Host "1. Check the Docker Desktop system tray icon" -ForegroundColor White
    Write-Host "2. Wait for it to show 'Docker Desktop is running'" -ForegroundColor White
    Write-Host "3. Try running 'docker ps' manually" -ForegroundColor White
    Write-Host "4. If issues persist, restart Docker Desktop" -ForegroundColor White
}

Write-Host "`n🔧 Troubleshooting Tips:" -ForegroundColor Cyan
Write-Host "• If Docker Desktop won't start: Restart as Administrator" -ForegroundColor White
Write-Host "• If still having issues: Restart your computer" -ForegroundColor White
Write-Host "• Check Windows Event Viewer for Docker-related errors" -ForegroundColor White
Write-Host "• Ensure virtualization is enabled in BIOS" -ForegroundColor White
