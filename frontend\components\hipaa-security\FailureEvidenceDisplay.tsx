import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Copy, ExternalLink, Code, AlertTriangle } from 'lucide-react';
import { FailureEvidence } from '@/types/hipaa-security';

interface FailureEvidenceDisplayProps {
  evidence: FailureEvidence[];
}

export const FailureEvidenceDisplay: React.FC<FailureEvidenceDisplayProps> = ({ evidence }) => {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      // Failed to copy to clipboard - silently fail
    }
  };

  const getLanguageFromElementType = (elementType: string): string => {
    switch (elementType) {
      case 'html':
        return 'html';
      case 'javascript':
        return 'javascript';
      case 'header':
        return 'http';
      case 'response':
        return 'http';
      case 'cookie':
        return 'http';
      case 'form':
        return 'html';
      default:
        return 'text';
    }
  };

  const getElementTypeIcon = (elementType: string) => {
    switch (elementType) {
      case 'html':
      case 'form':
        return <Code className="h-4 w-4" />;
      case 'javascript':
        return <Code className="h-4 w-4 text-yellow-500" />;
      case 'header':
      case 'response':
      case 'cookie':
        return <ExternalLink className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  if (evidence.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-red-800 flex items-center gap-2">
        <AlertTriangle className="h-4 w-4" />
        Failure Evidence ({evidence.length} issue{evidence.length !== 1 ? 's' : ''})
      </h4>

      {evidence.map((item, index) => (
        <Card key={index} className="border-red-200">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm flex items-center gap-2">
                {getElementTypeIcon(item.elementType)}
                {item.location}
                <Badge variant="outline" className="text-xs">
                  {item.elementType}
                </Badge>
                {item.lineNumber && (
                  <Badge variant="secondary" className="text-xs">
                    Line {item.lineNumber}
                  </Badge>
                )}
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(item.actualCode, index)}
                className="h-8 w-8 p-0"
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Problem Description */}
            <div className="bg-red-50 p-3 rounded">
              <p className="text-sm text-red-700">{item.context}</p>
            </div>

            {/* Code Evidence */}
            <div>
              <h5 className="text-sm font-medium mb-2 text-gray-700">Problematic Code:</h5>
              <div className="relative">
                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
                  <code className={`language-${getLanguageFromElementType(item.elementType)}`}>
                    {item.actualCode}
                  </code>
                </pre>
                {copiedIndex === index && (
                  <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                    Copied!
                  </div>
                )}
              </div>
            </div>

            {/* Expected Behavior */}
            <div className="bg-green-50 p-3 rounded">
              <h5 className="text-sm font-medium mb-1 text-green-800">Expected Behavior:</h5>
              <p className="text-sm text-green-700">{item.expectedBehavior}</p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
