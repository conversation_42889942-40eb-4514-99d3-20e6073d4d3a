@echo off
echo 🔍 Verifying Permanent Docker WSL2 Fix
echo ======================================
echo.

echo ✅ VERIFICATION CHECKLIST:
echo.

echo 1. Checking WSL2 status...
wsl --status 2>nul
if %errorlevel% equ 0 (
    echo ✅ WSL2 is available and responsive
) else (
    echo ❌ WSL2 not responding
)

echo.
echo 2. Checking WSL distributions...
wsl --list --verbose
echo.

echo 3. Checking WSL network share...
dir "\\wsl$" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ WSL network share (\\wsl$) is accessible
) else (
    echo ❌ WSL network share not accessible
)

echo.
echo 4. Checking Docker Desktop status...
tasklist | findstr "Docker Desktop" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker Desktop process is running
) else (
    echo ⚠️ Docker Desktop not running (this is OK if you haven't started it)
)

echo.
echo 5. Checking Docker daemon...
docker version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker daemon is responding
    docker --version
) else (
    echo ⚠️ Docker daemon not responding (start Docker Desktop first)
)

echo.
echo 6. Checking prevention measures...
schtasks /query /tn "WSL2 Docker Guardian" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Automatic prevention task is installed
) else (
    echo ❌ Automatic prevention task not found
)

echo.
echo 7. Checking WSL configuration...
if exist "%USERPROFILE%\.wslconfig" (
    echo ✅ Optimized WSL configuration present
    echo Configuration:
    type "%USERPROFILE%\.wslconfig"
) else (
    echo ⚠️ WSL configuration file not found
)

echo.
echo 8. Checking Windows services...
sc query LxssManager | findstr "RUNNING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ LxssManager service is running
) else (
    echo ❌ LxssManager service not running
)

sc query lanmanserver | findstr "RUNNING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Network server service is running
) else (
    echo ❌ Network server service not running
)

echo.
echo 9. Testing project Docker setup...
if exist "docker-compose.yml" (
    echo ✅ Found docker-compose.yml
    echo Testing configuration...
    docker-compose config >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Docker Compose configuration is valid
    ) else (
        echo ❌ Docker Compose configuration has errors
    )
) else (
    echo ⚠️ docker-compose.yml not found (run from project directory)
)

echo.
echo 📊 VERIFICATION SUMMARY:
echo ========================

REM Count successful checks
set /a total_checks=0
set /a passed_checks=0

REM This is a simplified summary - in a real script you'd track each check
echo.
echo If most checks show ✅, your permanent fix is working correctly.
echo If you see ❌ errors, you may need to:
echo   1. Restart your computer
echo   2. Re-run: FIX_DOCKER_PERMANENTLY.bat
echo   3. Start Docker Desktop manually
echo.

echo 🚀 To test your HIPAA security system:
echo   1. Start Docker Desktop (if not running)
echo   2. Run: docker-compose up -d
echo   3. Check: docker-compose ps
echo   4. Visit: http://localhost:3001/health
echo.

pause
