import { HipaaPrivacyScanResult, HipaaPrivacyScanConfig } from '@/types/hipaa-privacy';

export interface StartPrivacyScanRequest {
  targetUrl: string;
  timeout?: number;
  maxRedirects?: number;
  userAgent?: string;
  includeSubdomains?: boolean;
  enableLevel1?: boolean;
  enableLevel2?: boolean;
  enableLevel3?: boolean;
  cacheResults?: boolean;
  generateReport?: boolean;
}

export interface StartPrivacyScanResponse {
  success: boolean;
  data: {
    scanId: string;
    status: 'completed' | 'pending' | 'running';
    message: string;
    result?: HipaaPrivacyScanResult;
  };
}

export interface PrivacyScanStatusResponse {
  scanId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
  message?: string;
  result?: HipaaPrivacyScanResult;
}

class HipaaPrivacyApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || process.env.NEXT_PUBLIC_API_URL || '/api/v1';
  }

  async startScan(request: StartPrivacyScanRequest): Promise<StartPrivacyScanResponse> {
    const response = await fetch(`${this.baseUrl}/hipaa-privacy/scan`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      let errorMessage = 'Failed to start HIPAA privacy scan';
      try {
        const error = await response.json();
        errorMessage = error.message || error.error || errorMessage;
      } catch (parseError) {
        // If we can't parse JSON, use the response status text
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }
      throw new Error(errorMessage);
    }

    let result;
    try {
      result = await response.json();
    } catch (parseError) {
      throw new Error('Invalid response format from server');
    }

    if (!result.success) {
      throw new Error(result.error || 'Failed to start HIPAA privacy scan');
    }

    return result;
  }

  async getScanStatus(scanId: string): Promise<PrivacyScanStatusResponse> {
    const response = await fetch(`${this.baseUrl}/hipaa-privacy/scan/${scanId}/status`);

    if (!response.ok) {
      let errorMessage = 'Failed to get scan status';
      try {
        const error = await response.json();
        errorMessage = error.message || error.error || errorMessage;
      } catch (parseError) {
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }
      throw new Error(errorMessage);
    }

    try {
      return await response.json();
    } catch (parseError) {
      throw new Error('Invalid response format from server');
    }
  }

  async getScanResult(scanId: string): Promise<HipaaPrivacyScanResult> {
    const response = await fetch(`${this.baseUrl}/hipaa-privacy/scan/${scanId}/result`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get scan result');
    }

    return response.json();
  }

  async getAllScans(limit: number = 50): Promise<HipaaPrivacyScanResult[]> {
    const response = await fetch(`${this.baseUrl}/hipaa-privacy/scans?limit=${limit}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get scans');
    }

    return response.json();
  }

  async deleteScan(scanId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/hipaa-privacy/scan/${scanId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete scan');
    }
  }

  async exportScanReport(scanId: string, format: 'pdf' | 'json' = 'pdf'): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/hipaa-privacy/scan/${scanId}/export?format=${format}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to export scan report');
    }

    return response.blob();
  }

  // Polling utility for scan completion
  async pollScanCompletion(
    scanId: string,
    onProgress?: (progress: number) => void,
    maxWaitTime: number = 30 * 60 * 1000 // 30 minutes
  ): Promise<HipaaPrivacyScanResult> {
    const startTime = Date.now();
    const pollInterval = 5000; // 5 seconds

    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const status = await this.getScanStatus(scanId);

          if (onProgress && status.progress !== undefined) {
            onProgress(status.progress);
          }

          if (status.status === 'completed' && status.result) {
            resolve(status.result);
            return;
          }

          if (status.status === 'failed') {
            reject(new Error(status.message || 'Scan failed'));
            return;
          }

          // Check timeout
          if (Date.now() - startTime > maxWaitTime) {
            reject(new Error('Scan timeout exceeded'));
            return;
          }

          // Continue polling
          setTimeout(poll, pollInterval);
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }
}

export const hipaaPrivacyApi = new HipaaPrivacyApiService();
