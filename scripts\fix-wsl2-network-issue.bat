@echo off
echo 🔧 WSL2 Network Issue Fix for Docker Desktop
echo ============================================
echo.
echo Error: "The network name cannot be found" (\\wsl$\docker-desktop)
echo This is a WSL2 networking issue that prevents Docker Desktop from starting.
echo.

echo Step 1: Stopping Docker Desktop and WSL...
taskkill /F /IM "Docker Desktop.exe" >nul 2>&1
net stop "Docker Desktop Service" >nul 2>&1
echo ✅ Docker Desktop stopped

echo Shutting down all WSL distributions...
wsl --shutdown
timeout /t 5 /nobreak >nul
echo ✅ WSL shutdown completed

echo Step 2: Restarting WSL service...
net stop LxssManager >nul 2>&1
net start LxssManager >nul 2>&1
echo ✅ WSL service restarted

echo Step 3: Checking WSL distributions...
echo Current WSL distributions:
wsl --list --verbose

echo Step 4: Unregistering Docker WSL distributions...
echo This will remove Docker's WSL distributions and force recreation
wsl --unregister docker-desktop >nul 2>&1
wsl --unregister docker-desktop-data >nul 2>&1
echo ✅ Docker WSL distributions unregistered

echo Step 5: Clearing WSL network cache...
echo Flushing DNS and resetting network...
ipconfig /flushdns >nul 2>&1
netsh winsock reset >nul 2>&1
netsh int ip reset >nul 2>&1
echo ✅ Network cache cleared

echo Step 6: Restarting network services...
net stop Winmgmt /y >nul 2>&1
net start Winmgmt >nul 2>&1
net stop "Network Location Awareness" /y >nul 2>&1
net start "Network Location Awareness" >nul 2>&1
echo ✅ Network services restarted

echo Step 7: Checking WSL2 kernel...
echo Checking if WSL2 kernel is up to date...
wsl --update >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ WSL2 kernel updated
) else (
    echo ⚠️ WSL2 kernel update failed or not needed
)

echo Step 8: Setting WSL2 as default...
wsl --set-default-version 2 >nul 2>&1
echo ✅ WSL2 set as default

echo Step 9: Restarting WSL completely...
wsl --shutdown
timeout /t 10 /nobreak >nul
net stop LxssManager >nul 2>&1
timeout /t 3 /nobreak >nul
net start LxssManager >nul 2>&1
timeout /t 5 /nobreak >nul
echo ✅ WSL completely restarted

echo Step 10: Testing WSL network access...
echo Testing if \\wsl$ is accessible...
dir \\wsl$ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ WSL network share is accessible
) else (
    echo ⚠️ WSL network share still not accessible - will be created when Docker starts
)

echo Step 11: Starting Docker Desktop...
echo Looking for Docker Desktop executable...

set DOCKER_PATH=""
if exist "%ProgramFiles%\Docker\Docker\Docker Desktop.exe" (
    set DOCKER_PATH="%ProgramFiles%\Docker\Docker\Docker Desktop.exe"
) else if exist "%ProgramFiles(x86)%\Docker\Docker\Docker Desktop.exe" (
    set DOCKER_PATH="%ProgramFiles(x86)%\Docker\Docker Desktop.exe"
) else if exist "%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe" (
    set DOCKER_PATH="%LOCALAPPDATA%\Programs\Docker\Docker\Docker Desktop.exe"
) else (
    echo ❌ Docker Desktop executable not found
    pause
    exit /b 1
)

echo ✅ Found Docker Desktop at: %DOCKER_PATH%
echo Starting Docker Desktop...
start "" %DOCKER_PATH%

echo Step 12: Waiting for Docker Desktop to recreate WSL distributions...
echo This process will:
echo • Create new docker-desktop WSL distribution
echo • Create new docker-desktop-data WSL distribution  
echo • Set up WSL network share (\\wsl$\docker-desktop)
echo • Initialize Docker daemon
echo.
echo This may take 3-5 minutes. Please be patient...

set /a attempts=0
:wait_loop
set /a attempts+=1
echo.
echo Attempt %attempts%/20: Checking Docker daemon and WSL distributions...

REM Check if WSL distributions are created
wsl --list --quiet | findstr "docker-desktop" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker WSL distributions detected
    
    REM Check if Docker daemon is responding
    docker version >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Docker daemon is responding
        goto :success
    ) else (
        echo ⏳ Docker daemon still starting...
    )
) else (
    echo ⏳ Docker WSL distributions still being created...
)

if %attempts% geq 20 (
    echo ⚠️ Docker Desktop taking longer than expected
    goto :manual_check
)

timeout /t 15 /nobreak >nul
goto :wait_loop

:success
echo.
echo 🎉 WSL2 network issue fixed successfully!
echo.
echo Final verification:
echo WSL distributions:
wsl --list --verbose
echo.
echo Docker status:
docker --version
docker ps
echo.
echo ✅ Docker Desktop is now running with properly configured WSL2
goto :end

:manual_check
echo.
echo ⚠️ Docker Desktop may still be initializing or needs additional attention
echo.
echo 📋 Manual checks:
echo 1. Check Docker Desktop system tray icon
echo 2. Look for any error messages in Docker Desktop
echo 3. Check WSL distributions: wsl --list --verbose
echo 4. Try accessing: \\wsl$\docker-desktop in File Explorer
echo.
echo If issues persist, try:
echo • Restart your computer
echo • Update WSL2 kernel manually from: https://aka.ms/wsl2kernel
echo • Reinstall Docker Desktop
echo.

:end
echo.
echo 🔧 Additional WSL2 troubleshooting commands:
echo.
echo Check WSL status:           wsl --status
echo List distributions:         wsl --list --verbose  
echo Update WSL:                 wsl --update
echo Shutdown WSL:               wsl --shutdown
echo Access Docker WSL:          wsl -d docker-desktop
echo.
echo If \\wsl$ network share issues persist:
echo • Check Windows Features: "Windows Subsystem for Linux"
echo • Restart "LanmanServer" service
echo • Run: sfc /scannow (as Administrator)
echo.
pause
