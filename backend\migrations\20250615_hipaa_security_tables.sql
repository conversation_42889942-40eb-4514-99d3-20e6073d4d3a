-- HIPAA Security Scan Results Table
CREATE TABLE hipaa_security_scans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    target_url VARCHAR(2048) NOT NULL,
    scan_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    scan_duration INTEGER, -- in milliseconds
    overall_score DECIMAL(5,2),
    risk_level VARCHAR(20) CHECK (risk_level IN ('critical', 'high', 'medium', 'low')),
    pages_scanned TEXT[], -- array of scanned pages
    tools_used TEXT[], -- array of tools used
    scan_status VARCHAR(20) DEFAULT 'pending' CHECK (scan_status IN ('pending', 'running', 'completed', 'failed')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- HIPAA Security Test Results Table
CREATE TABLE hipaa_security_test_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scan_id UUID NOT NULL REFERENCES hipaa_security_scans(id) ON DELETE CASCADE,
    test_id VARCHAR(100) NOT NULL,
    test_name VARCHAR(255) NOT NULL,
    hipaa_section VARCHAR(50) NOT NULL, -- e.g., '164.312(a)(1)'
    category VARCHAR(50) NOT NULL CHECK (category IN ('technical', 'administrative', 'organizational', 'physical')),
    passed BOOLEAN NOT NULL,
    risk_level VARCHAR(20) CHECK (risk_level IN ('critical', 'high', 'medium', 'low')),
    description TEXT,
    failure_reason TEXT,
    evidence JSONB, -- stores evidence data
    pages_tested TEXT[],
    remediation_priority INTEGER CHECK (remediation_priority BETWEEN 1 AND 5),
    recommended_action TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- HIPAA Security Vulnerabilities Table
CREATE TABLE hipaa_security_vulnerabilities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scan_id UUID NOT NULL REFERENCES hipaa_security_scans(id) ON DELETE CASCADE,
    vulnerability_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) CHECK (severity IN ('critical', 'high', 'medium', 'low', 'info')),
    location VARCHAR(2048) NOT NULL,
    description TEXT NOT NULL,
    evidence JSONB,
    cwe_id INTEGER,
    owasp_category VARCHAR(100),
    remediation_guidance TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- HIPAA Security Failure Evidence Table
CREATE TABLE hipaa_security_failure_evidence (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_result_id UUID NOT NULL REFERENCES hipaa_security_test_results(id) ON DELETE CASCADE,
    location VARCHAR(2048) NOT NULL,
    element_type VARCHAR(50) CHECK (element_type IN ('header', 'html', 'javascript', 'response', 'cookie', 'form')),
    actual_code TEXT NOT NULL,
    expected_behavior TEXT NOT NULL,
    line_number INTEGER,
    context TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_hipaa_security_scans_target_url ON hipaa_security_scans(target_url);
CREATE INDEX idx_hipaa_security_scans_timestamp ON hipaa_security_scans(scan_timestamp);
CREATE INDEX idx_hipaa_security_scans_risk_level ON hipaa_security_scans(risk_level);
CREATE INDEX idx_hipaa_security_test_results_scan_id ON hipaa_security_test_results(scan_id);
CREATE INDEX idx_hipaa_security_test_results_passed ON hipaa_security_test_results(passed);
CREATE INDEX idx_hipaa_security_test_results_category ON hipaa_security_test_results(category);
CREATE INDEX idx_hipaa_security_vulnerabilities_scan_id ON hipaa_security_vulnerabilities(scan_id);
CREATE INDEX idx_hipaa_security_vulnerabilities_severity ON hipaa_security_vulnerabilities(severity);
