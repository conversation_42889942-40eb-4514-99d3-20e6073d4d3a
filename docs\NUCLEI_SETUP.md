# Nuclei Vulnerability Scanner Setup

Comply Checker now uses **Nuclei** as its primary vulnerability scanner instead of OWASP ZAP. Nuclei is a fast, lightweight, and template-based vulnerability scanner that's perfect for HIPAA compliance checks.

## Why Nuclei?

- **Lightweight**: Much faster and lighter than OWASP ZAP
- **Template-based**: 5000+ community templates for various vulnerabilities
- **HIPAA-focused**: Specific templates for security headers, SSL/TLS, privacy policies
- **Easy installation**: Single binary, no complex setup
- **Active community**: Constantly updated templates
- **Better for CI/CD**: Faster scans, better for automated workflows

## Installation

### Automatic Installation

#### Windows (PowerShell)
```powershell
.\scripts\install-nuclei.ps1
```

#### Linux/macOS (Bash)
```bash
chmod +x scripts/install-nuclei.sh
./scripts/install-nuclei.sh
```

### Manual Installation

1. **Download Nuclei**:
   - Go to [Nuclei Releases](https://github.com/projectdiscovery/nuclei/releases)
   - Download the appropriate binary for your OS
   - Extract to a directory (e.g., `./tools/nuclei/`)

2. **Set Environment Variable**:
   ```bash
   # Linux/macOS
   export NUCLEI_PATH=/path/to/nuclei/binary
   
   # Windows
   set NUCLEI_PATH=C:\path\to\nuclei.exe
   ```

3. **Update Templates**:
   ```bash
   nuclei -update-templates
   ```

## Configuration

### Environment Variables

- `NUCLEI_PATH`: Path to the Nuclei binary (optional if in PATH)
- `NUCLEI_TEMPLATES_PATH`: Path to templates directory (optional)

### Example .env Configuration
```env
# Nuclei Configuration
NUCLEI_PATH=./tools/nuclei/nuclei
NUCLEI_TEMPLATES_PATH=./tools/nuclei/nuclei-templates
```

## HIPAA-Focused Scanning

Nuclei scans for HIPAA-relevant vulnerabilities using these template categories:

### Security Categories
- **SSL/TLS**: Certificate validation, encryption strength
- **Headers**: Security headers (HSTS, CSP, X-Frame-Options, etc.)
- **Privacy**: Privacy policy detection and analysis
- **Authentication**: Login security, session management
- **Disclosure**: Information leakage, error messages
- **Configuration**: Server misconfigurations
- **Exposure**: Sensitive file exposure

### Specific Templates Used
- `http/misconfiguration/security-headers-check.yaml`
- `http/vulnerabilities/generic/error-based-sql-injection.yaml`
- `http/exposures/configs/web-config.yaml`
- `http/exposures/files/sensitive-files.yaml`
- SSL/TLS validation templates
- Privacy policy detection templates

## Comparison: Nuclei vs OWASP ZAP

| Feature | Nuclei | OWASP ZAP |
|---------|--------|-----------|
| **Installation** | Single binary | Complex setup, Java required |
| **Speed** | Very fast (seconds) | Slow (minutes) |
| **Resource Usage** | Lightweight | Heavy (requires proxy) |
| **Templates** | 5000+ community templates | Built-in rules |
| **HIPAA Focus** | Excellent template coverage | General web security |
| **Maintenance** | Auto-updating templates | Manual configuration |
| **CI/CD Integration** | Excellent | Difficult |
| **Learning Curve** | Easy | Steep |

## Usage in Comply Checker

When you run a HIPAA security scan, Nuclei will:

1. **Check availability**: Verify Nuclei is installed and accessible
2. **Update templates**: Ensure latest vulnerability templates
3. **Run targeted scans**: Execute HIPAA-relevant template categories
4. **Parse results**: Convert findings to standard vulnerability format
5. **Fallback**: Use basic checks if Nuclei is unavailable

## Troubleshooting

### Common Issues

1. **"Nuclei not available"**
   - Ensure Nuclei is installed and in PATH or NUCLEI_PATH is set
   - Check binary permissions (Linux/macOS: `chmod +x nuclei`)

2. **"Template update failed"**
   - Check internet connectivity
   - Run manually: `nuclei -update-templates`

3. **"No vulnerabilities found"**
   - This is normal for well-secured sites
   - Check logs for template execution details

### Verification

Test your Nuclei installation:
```bash
# Check version
nuclei -version

# Update templates
nuclei -update-templates

# Test scan
nuclei -u https://example.com -tags ssl,headers -silent
```

## Benefits for HIPAA Compliance

1. **Faster Scans**: Complete vulnerability assessment in seconds vs minutes
2. **Better Coverage**: Specific templates for HIPAA requirements
3. **Easier Maintenance**: Auto-updating templates, no complex configuration
4. **Reliable Results**: Community-maintained templates, fewer false positives
5. **Better Integration**: Works seamlessly with existing infrastructure

## Migration from ZAP

If you were previously using OWASP ZAP:

1. **No configuration changes needed**: Comply Checker automatically detects and uses Nuclei
2. **Better results**: More targeted HIPAA-specific vulnerability detection
3. **Faster scans**: Significant performance improvement
4. **Easier setup**: No need to run ZAP proxy server

The system will automatically fall back to basic vulnerability checks if neither Nuclei nor ZAP is available.
