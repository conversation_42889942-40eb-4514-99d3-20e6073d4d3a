module.exports = {
  root: true, // Important: ESLint stops looking in parent folders
  extends: [
    '../.eslintrc.js', // Extend the root ESLint configuration
  ],
  parserOptions: {
    project: ['./tsconfig.json'], // Link to backend's tsconfig
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  env: {
    node: true,
    es6: true,
    jest: true, // For test files
  },
  rules: {
    // Backend-specific rules
    'no-console': 'off', // Allow console in backend for logging
    '@typescript-eslint/no-explicit-any': 'warn', // Set to warn for now
    'require-jsdoc': 'off', // Disable JSDoc requirement for backend
    'valid-jsdoc': 'off', // Disable JSDoc validation for backend
    '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],

    // Allow require() in backend for certain cases
    '@typescript-eslint/no-var-requires': 'off',

    // Backend often uses process.env
    'no-process-env': 'off',
  },
  overrides: [
    {
      files: ['*.test.ts', '*.spec.ts', '**/__tests__/**/*.ts'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off', // Allow any in tests
        'require-jsdoc': 'off', // No JSDoc required in tests
      },
    },
    {
      files: ['migrations/*.ts', 'seeds/*.ts'],
      rules: {
        'require-jsdoc': 'off', // Disable JSDoc requirement for migrations and seeds
        '@typescript-eslint/no-explicit-any': 'off', // Allow any in migrations/seeds
      },
    },
  ],
};
