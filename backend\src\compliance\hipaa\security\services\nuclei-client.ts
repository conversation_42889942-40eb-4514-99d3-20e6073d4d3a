import { spawn } from 'child_process';
import path from 'path';
import { NucleiScanOptions, NucleiVulnerability, NucleiScanResult } from '../types';
import { HttpClient, HttpResponse } from './http-client';

export class NucleiClient {
  private nucleiPath: string;
  private templatesPath: string;
  private httpClient: HttpClient;

  constructor() {
    // Nuclei binary path - will be downloaded/installed
    this.nucleiPath = process.env.NUCLEI_PATH || 'nuclei';
    this.templatesPath =
      process.env.NUCLEI_TEMPLATES_PATH || path.join(process.cwd(), 'nuclei-templates');

    // Initialize HTTP client for content analysis and URL discovery
    this.httpClient = new HttpClient({
      timeout: 30000,
      maxRedirects: 5,
      userAgent: 'HIPAA-Security-Scanner/1.0',
      retryAttempts: 3,
    });
  }

  /**
   * Check if Nuclei is available and working
   */
  async isAvailable(): Promise<boolean> {
    try {
      const result = await this.runNucleiCommand(['-version'], 5000);
      // Nuclei outputs version info to stderr, so check both stdout and stderr
      const hasNucleiOutput = result.output.includes('Nuclei') || result.error.includes('Nuclei');
      return result.success && hasNucleiOutput;
    } catch (error) {
      console.warn(
        '⚠️ Nuclei not available:',
        error instanceof Error ? error.message : 'Unknown error',
      );
      return false;
    }
  }

  /**
   * Update Nuclei templates to latest version
   */
  async updateTemplates(): Promise<boolean> {
    try {
      console.log('📥 Updating Nuclei templates...');
      const result = await this.runNucleiCommand(['-update-templates'], 30000);

      if (result.success) {
        console.log('✅ Nuclei templates updated successfully');
        return true;
      } else {
        console.warn('⚠️ Failed to update templates:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Template update failed:', error);
      return false;
    }
  }

  /**
   * Run HIPAA-focused vulnerability scan
   */
  async scanForHipaaVulnerabilities(options: NucleiScanOptions): Promise<NucleiVulnerability[]> {
    const vulnerabilities: NucleiVulnerability[] = [];

    try {
      console.log(`🔍 Starting Nuclei HIPAA security scan for: ${options.targetUrl}`);

      // Define HIPAA-relevant template tags
      const hipaaTemplates = [
        'ssl', // SSL/TLS vulnerabilities
        'tls', // TLS configuration issues
        'headers', // Security headers
        'privacy', // Privacy policy checks
        'disclosure', // Information disclosure
        'auth', // Authentication issues
        'session', // Session management
        'encryption', // Encryption issues
        'compliance', // Compliance checks
        'config', // Configuration issues
        'exposure', // Data exposure
      ];

      // Run scan with HIPAA-relevant templates
      for (const tag of hipaaTemplates) {
        console.log(`🔍 Scanning with ${tag} templates...`);

        const tagVulns = await this.runTemplateTagScan(options.targetUrl, tag, options.timeout);
        vulnerabilities.push(...tagVulns);

        // Add small delay between scans to be respectful
        await this.delay(1000);
      }

      // Run additional specific templates for HIPAA
      const specificTemplates = [
        'http/misconfiguration/security-headers-check.yaml',
        'http/vulnerabilities/generic/error-based-sql-injection.yaml',
        'http/exposures/configs/web-config.yaml',
        'http/exposures/files/sensitive-files.yaml',
      ];

      for (const template of specificTemplates) {
        console.log(`🔍 Running specific template: ${template}`);
        const templateVulns = await this.runSpecificTemplate(
          options.targetUrl,
          template,
          options.timeout,
        );
        vulnerabilities.push(...templateVulns);
        await this.delay(500);
      }

      console.log(`🔍 Nuclei scan completed: ${vulnerabilities.length} vulnerabilities found`);
      return vulnerabilities;
    } catch (error) {
      console.error('❌ Nuclei scan failed:', error);
      return [];
    }
  }

  /**
   * Run scan with specific template tag
   */
  private async runTemplateTagScan(
    targetUrl: string,
    tag: string,
    timeout: number = 30000,
  ): Promise<NucleiVulnerability[]> {
    try {
      const args = [
        '-u',
        targetUrl,
        '-tags',
        tag,
        '-json',
        '-silent',
        '-timeout',
        '10',
        '-retries',
        '2',
        '-rate-limit',
        '10',
      ];

      const result = await this.runNucleiCommand(args, timeout);

      if (result.success && result.output) {
        return this.parseNucleiOutput(result.output);
      }

      return [];
    } catch (error) {
      console.warn(`⚠️ Template tag scan failed for ${tag}:`, error);
      return [];
    }
  }

  /**
   * Run specific template
   */
  private async runSpecificTemplate(
    targetUrl: string,
    template: string,
    timeout: number = 30000,
  ): Promise<NucleiVulnerability[]> {
    try {
      const args = [
        '-u',
        targetUrl,
        '-t',
        template,
        '-json',
        '-silent',
        '-timeout',
        '10',
        '-retries',
        '1',
      ];

      const result = await this.runNucleiCommand(args, timeout);

      if (result.success && result.output) {
        return this.parseNucleiOutput(result.output);
      }

      return [];
    } catch (error) {
      console.warn(`⚠️ Specific template scan failed for ${template}:`, error);
      return [];
    }
  }

  /**
   * Parse Nuclei JSON output into vulnerabilities
   */
  private parseNucleiOutput(output: string): NucleiVulnerability[] {
    const vulnerabilities: NucleiVulnerability[] = [];

    try {
      const lines = output
        .trim()
        .split('\n')
        .filter((line) => line.trim());

      for (const line of lines) {
        try {
          const finding: {
            info?: {
              name?: string;
              severity?: string;
              description?: string;
              remediation?: string;
              tags?: string[];
              reference?: string[];
            };
            template_id?: string;
            matched_at: string; // Ensure this is required since we check for it
            extracted_results?: Record<string, unknown>[];
          } = JSON.parse(line);

          if (finding.info && finding.matched_at) {
            vulnerabilities.push({
              id: finding.info.name || finding.template_id || 'unknown',
              type: finding.info.name || 'Unknown Vulnerability',
              severity: this.mapNucleiSeverityToStandard(finding.info.severity || 'medium'),
              location: finding.matched_at,
              description: finding.info.description || 'No description available',
              evidence: {
                nucleiTemplate: finding.template_id || '',
                templateInfo: finding.info,
                matchedAt: finding.matched_at,
                extractedResults: finding.extracted_results || [],
              },
              remediationGuidance:
                finding.info.remediation ||
                this.getDefaultRemediation(finding.info.severity || 'medium'),
              tags: finding.info.tags || [],
              reference: finding.info.reference || [],
            });
          }
        } catch (parseError) {
          console.warn('⚠️ Failed to parse Nuclei finding:', parseError);
        }
      }
    } catch (error) {
      console.error('❌ Failed to parse Nuclei output:', error);
    }

    return vulnerabilities;
  }

  /**
   * Map Nuclei severity to standard severity levels
   */
  private mapNucleiSeverityToStandard(
    nucleiSeverity: string | undefined,
  ): 'critical' | 'high' | 'medium' | 'low' {
    switch (nucleiSeverity?.toLowerCase()) {
      case 'critical':
        return 'critical';
      case 'high':
        return 'high';
      case 'medium':
        return 'medium';
      case 'low':
      case 'info':
      default:
        return 'low';
    }
  }

  /**
   * Get default remediation guidance based on severity
   */
  private getDefaultRemediation(severity: string | undefined): string {
    switch (severity?.toLowerCase()) {
      case 'critical':
        return 'Immediate action required. This vulnerability poses a critical risk to HIPAA compliance.';
      case 'high':
        return 'High priority fix required. Address this vulnerability to maintain HIPAA compliance.';
      case 'medium':
        return 'Medium priority fix recommended for optimal HIPAA security posture.';
      case 'low':
      case 'info':
      default:
        return 'Low priority informational finding. Consider addressing for comprehensive security.';
    }
  }

  /**
   * Run Nuclei command and return result
   */
  private async runNucleiCommand(
    args: string[],
    timeout: number = 30000,
  ): Promise<NucleiScanResult> {
    return new Promise((resolve) => {
      let output = '';
      let errorOutput = '';

      const process = spawn(this.nucleiPath, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
      });

      const timeoutId = setTimeout(() => {
        process.kill('SIGTERM');
        resolve({
          success: false,
          output: '',
          error: 'Command timeout',
        });
      }, timeout);

      process.stdout?.on('data', (data) => {
        output += data.toString();
      });

      process.stderr?.on('data', (data) => {
        errorOutput += data.toString();
      });

      process.on('close', (code) => {
        clearTimeout(timeoutId);
        resolve({
          success: code === 0,
          output: output.trim(),
          error: errorOutput.trim(),
        });
      });

      process.on('error', (error) => {
        clearTimeout(timeoutId);
        resolve({
          success: false,
          output: '',
          error: error.message,
        });
      });
    });
  }

  /**
   * Discover URLs from a target website (replaces ZAP spider functionality)
   */
  async discoverUrls(targetUrl: string, maxPages: number = 15): Promise<string[]> {
    try {
      console.log(`🕷️ Discovering URLs from ${targetUrl}...`);

      const discoveredUrls = new Set<string>([targetUrl]);
      const urlsToProcess = [targetUrl];
      const processedUrls = new Set<string>();

      while (urlsToProcess.length > 0 && discoveredUrls.size < maxPages) {
        const currentUrl = urlsToProcess.shift()!;

        if (processedUrls.has(currentUrl)) {
          continue;
        }

        processedUrls.add(currentUrl);

        try {
          const links = await this.httpClient.discoverLinks(currentUrl);

          for (const link of links) {
            if (discoveredUrls.size >= maxPages) break;

            if (!discoveredUrls.has(link) && !processedUrls.has(link)) {
              discoveredUrls.add(link);

              // Only add to processing queue if we haven't reached the limit
              if (discoveredUrls.size < maxPages) {
                urlsToProcess.push(link);
              }
            }
          }

          // Small delay to be respectful
          await this.delay(500);
        } catch (error) {
          console.warn(`⚠️ Failed to discover links from ${currentUrl}:`, error);
        }
      }

      const finalUrls = Array.from(discoveredUrls);
      console.log(`🔗 Discovered ${finalUrls.length} URLs total`);
      return finalUrls;
    } catch (error) {
      console.error(`❌ URL discovery failed:`, error);
      return [targetUrl]; // Return at least the target URL
    }
  }

  /**
   * Fetch content from a URL (replaces ZAP accessUrl functionality)
   */
  async fetchUrlContent(url: string): Promise<HttpResponse> {
    try {
      return await this.httpClient.fetchUrl(url);
    } catch (error) {
      console.error(`❌ Failed to fetch content from ${url}:`, error);
      return {
        statusCode: 0,
        responseHeaders: {},
        body: '',
        url,
        redirectChain: [],
      };
    }
  }

  /**
   * Scan multiple URLs for vulnerabilities
   */
  async scanMultipleUrls(
    urls: string[],
    options: Omit<NucleiScanOptions, 'targetUrl'>,
  ): Promise<NucleiVulnerability[]> {
    const allVulnerabilities: NucleiVulnerability[] = [];

    console.log(`🔍 Scanning ${urls.length} URLs with Nuclei...`);

    for (const url of urls) {
      try {
        console.log(`🎯 Scanning: ${url}`);

        const vulnerabilities = await this.scanForHipaaVulnerabilities({
          ...options,
          targetUrl: url,
        });

        allVulnerabilities.push(...vulnerabilities);

        // Small delay between scans
        await this.delay(1000);
      } catch (error) {
        console.warn(`⚠️ Failed to scan ${url}:`, error);
      }
    }

    console.log(`🔍 Found ${allVulnerabilities.length} total vulnerabilities across all URLs`);
    return allVulnerabilities;
  }

  /**
   * Simple delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
