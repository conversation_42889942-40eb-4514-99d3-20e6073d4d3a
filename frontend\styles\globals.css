@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  /* You can add base body styles here if needed */
}

@layer base {
  :root {
    --background: 0 0% 100%;

    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;

    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;

    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;

    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;

    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;

    --muted-foreground: 0 0% 25.1%;

    --accent: 0 0% 96.1%;

    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;

    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;

    --input: 0 0% 89.8%;

    --ring: 0 0% 3.9%;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --radius: 0.5rem;
    --success: 140 80% 96%;
    --success-foreground: 145 60% 30%;
    --warning: 45 90% 96%;
    --warning-foreground: 40 70% 35%;
  }
  .dark {
    --background: 0 0% 3.9%;

    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;

    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;

    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;

    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;

    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;

    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;

    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;

    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;

    --input: 0 0% 14.9%;

    --ring: 0 0% 83.1%;

    --chart-1: 220 70% 50%;

    --chart-2: 160 60% 45%;

    --chart-3: 30 80% 55%;

    --chart-4: 280 65% 60%;

    --chart-5: 340 75% 55%;
    --success: 145 65% 15%;
    --success-foreground: 140 70% 75%;
    --warning: 40 75% 20%;
    --warning-foreground: 45 85% 75%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced text visibility for guidance pages */
@layer utilities {
  .text-readable {
    @apply text-gray-900 dark:text-gray-100 !important;
  }

  .text-readable-muted {
    @apply text-gray-800 dark:text-gray-200 !important;
  }

  .text-readable-light {
    @apply text-gray-700 dark:text-gray-300 !important;
  }

  /* Force high contrast for all text elements */
  .text-high-contrast {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 500;
  }

  .text-medium-contrast {
    @apply text-gray-800 dark:text-gray-200 !important;
    font-weight: 400;
  }

  /* Template and code block styling */
  .template-text {
    @apply text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 !important;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .code-block {
    @apply text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-800 !important;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
  }

  /* Ensure good contrast for guidance content */
  .guidance-content {
    @apply text-gray-900 dark:text-gray-100 !important;
  }

  .guidance-content h1,
  .guidance-content h2,
  .guidance-content h3,
  .guidance-content h4,
  .guidance-content h5,
  .guidance-content h6 {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .guidance-content p,
  .guidance-content li,
  .guidance-content span,
  .guidance-content div {
    @apply text-gray-800 dark:text-gray-200 !important;
    line-height: 1.6;
  }

  .guidance-content .text-muted {
    @apply text-gray-700 dark:text-gray-300 !important;
  }

  /* Step-by-step guide styling */
  .step-guide {
    @apply text-gray-900 dark:text-gray-100 !important;
  }

  .step-guide .step-number {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 700;
    font-size: 1.125rem;
  }

  .step-guide .step-description {
    @apply text-gray-800 dark:text-gray-200 !important;
    line-height: 1.6;
  }

  /* Section headers */
  .section-header {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 600;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  /* Progress text */
  .progress-text {
    @apply text-gray-800 dark:text-gray-200 !important;
    font-weight: 500;
  }

  /* Card descriptions */
  .card-description {
    @apply text-gray-700 dark:text-gray-300 !important;
    line-height: 1.5;
  }

  /* List items */
  .guidance-list li {
    @apply text-gray-800 dark:text-gray-200 !important;
    margin-bottom: 0.5rem;
    line-height: 1.6;
  }

  /* Required elements styling */
  .required-elements {
    @apply text-gray-900 dark:text-gray-100 !important;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 0.5rem 0;
  }

  .dark .required-elements {
    background-color: #1f2937;
    border-color: #374151;
  }

  .required-elements h4 {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #111827 !important;
  }

  .dark .required-elements h4 {
    color: #f9fafb !important;
  }

  .required-elements span {
    @apply text-gray-900 dark:text-gray-100 !important;
    font-weight: 500;
    color: #111827 !important;
    line-height: 1.6;
  }

  .dark .required-elements span {
    color: #f9fafb !important;
  }

  .required-elements li {
    margin-bottom: 0.5rem;
  }

  /* Process steps styling */
  .process-steps {
    counter-reset: step-counter;
  }

  .process-step {
    counter-increment: step-counter;
    @apply text-gray-800 dark:text-gray-200 !important;
    position: relative;
    padding-left: 3rem;
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  .process-step::before {
    content: counter(step-counter);
    position: absolute;
    left: 0;
    top: 0;
    @apply bg-blue-600 text-white !important;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
  }

  /* Card and section improvements */
  .card-description {
    @apply text-gray-800 dark:text-gray-200 !important;
  }

  .section-header {
    @apply text-gray-900 dark:text-gray-100 !important;
  }
}
