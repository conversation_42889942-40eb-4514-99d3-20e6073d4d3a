import React from 'react';
import { Badge } from '@/components/ui/Badge'; // Assuming Badge is used for status

// Define interfaces for details based on backend types
// These should ideally be shared or derived from a common types definition
// if you had a shared types package between frontend and backend.
interface AdaCheckDetailFromBackend {
  element: string;
  message: string;
  passed: boolean;
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  htmlLocation?: string;
  imgSrc?: string;
  altText?: string | null;
}

interface PageTitleCheckDetails {
  info: string;
  target: string;
  foundTitle: string | null;
}

interface FindingDetailsDisplayProps {
  standard: string;
  details: Record<string, unknown> | string | unknown[] | null | undefined;
}

const FindingDetailsDisplay: React.FC<FindingDetailsDisplayProps> = ({ standard, details }) => {
  if (!details) {
    return <p className="mt-1 text-xs text-muted-foreground">No specific details provided.</p>;
  }

  if (typeof details === 'string') {
    return <p className="mt-1 text-xs text-muted-foreground">Details: {details}</p>;
  }

  if (standard.toLowerCase() === 'ada' && Array.isArray(details)) {
    const adaDetails = details as AdaCheckDetailFromBackend[]; // Type assertion
    if (adaDetails.length === 0) {
      return <p className="mt-1 text-xs text-muted-foreground">No specific image details found.</p>;
    }
    return (
      <div className="mt-2 space-y-2">
        <h4 className="text-xs font-semibold">Image Details:</h4>
        <ul className="list-disc pl-5 space-y-1 text-xs">
          {adaDetails.map((item, index) => (
            <li key={index}>
              {(() => {
                let displayStatusString: 'PASS' | 'FAIL' | 'WARN' | 'INFO';
                let badgeVariant: 'success' | 'destructive' | 'warning' | 'default';

                if (item.passed) {
                  displayStatusString = 'PASS';
                  badgeVariant = 'success';
                } else {
                  if (item.severity === 'critical' || item.severity === 'high') {
                    displayStatusString = 'FAIL';
                    badgeVariant = 'destructive';
                  } else if (item.severity === 'medium') {
                    displayStatusString = 'WARN';
                    badgeVariant = 'warning';
                  } else {
                    // low or info
                    displayStatusString = item.severity === 'info' ? 'INFO' : 'FAIL'; // Default to FAIL if not passed and not warn/info
                    badgeVariant = item.severity === 'info' ? 'default' : 'destructive';
                  }
                }

                return (
                  <>
                    <Badge variant={badgeVariant}>{displayStatusString}</Badge>
                    <code className="ml-2 bg-muted p-1 rounded text-xs whitespace-pre-wrap">
                      {item.htmlLocation || item.element}{' '}
                      {/* Display htmlLocation or element as imgTag was local interface only */}
                    </code>
                  </>
                );
              })()}
              <p className="ml-2 text-muted-foreground">{item.message}</p>
              {item.altText !== undefined && (
                <p className="ml-2 text-muted-foreground">Alt: "{item.altText}"</p>
              )}
            </li>
          ))}
        </ul>
      </div>
    );
  }

  if (
    standard.toLowerCase() === 'wcag' &&
    typeof details === 'object' &&
    details !== null &&
    'foundTitle' in details
  ) {
    const wcagDetails = details as unknown as PageTitleCheckDetails; // Type assertion
    return (
      <div className="mt-2 space-y-1 text-xs">
        <h4 className="text-xs font-semibold">Page Title Details:</h4>
        <p>
          Found Title:{' '}
          <code className="bg-muted p-1 rounded">{wcagDetails.foundTitle || 'N/A'}</code>
        </p>
        <p>Info: {wcagDetails.info}</p>
        {/* <p>Target: {wcagDetails.target}</p> */}{' '}
        {/* Target might be redundant if it's the main URL */}
      </div>
    );
  }

  // Fallback for other object structures - display as JSON
  if (typeof details === 'object' && details !== null) {
    return (
      <div className="mt-2">
        <h4 className="text-xs font-semibold">Raw Details:</h4>
        <pre className="bg-muted p-2 rounded-md text-xs overflow-x-auto">
          {JSON.stringify(details, null, 2)}
        </pre>
      </div>
    );
  }

  return <p className="mt-1 text-xs text-muted-foreground">Details format not recognized.</p>;
};

export default FindingDetailsDisplay;
