ScanService: Initiating new scan for Keycloak user 95c04ff6-1666-4d81-96d6-cad84104c89c, URL: https://www.athenahealth.com/, Standards: [hipaa]
ScanService: User found with ID: 30523b79-73c6-4932-bf65-0c5b0a3e024f
ScanService: Scan record created with ID: 3eb9de85-e31c-484f-b308-685e5dbd27d2
ScanService: Scan 3eb9de85-e31c-484f-b308-685e5dbd27d2 status updated to IN_PROGRESS.
ScanService: Performing HIPAA checks for scan 3eb9de85-e31c-484f-b308-685e5dbd27d2
ScanService: Using enhanced HIPAA orchestrator for scan 3eb9de85-e31c-484f-b308-685e5dbd27d2
HipaaOrchestrator: Starting comprehensive scan for https://www.athenahealth.com/ (ID: hipaa-1750008997249-a5cnw04t0)
HipaaOrchestrator: Executing 3 checks
HipaaOrchestrator: Executing batch 1 with 3 checks
HipaaOrchestrator: Starting check privacy-policy-presence
HipaaOrchestrator: Starting check hipaa-specific-content
HipaaOrchestrator: Starting check contact-information
🔍 [Privacy Policy Detection] Starting link detection for base URL: https://www.athenahealth.com/
📄 [Privacy Policy Detection] HTML content length: 363125
🎯 [Privacy Policy Detection] Using patterns: [
  '/privacy\\s*policy/i',
  '/privacy\\s*notice/i',
  '/hipaa\\s*notice/i',
  '/notice\\s*of\\s*privacy\\s*practices/i',
  '/privacy\\s*statement/i',
  '/data\\s*privacy/i',
  '/\\bprivacy\\b/i',
  '/data\\s*protection/i',
  '/cookie\\s*policy/i',
  '/terms\\s*.*\\s*privacy/i'
]
🎯 [Privacy Policy Detection] Found privacy policy link: {
  url: 'https://athenahealth.com/privacy-rights',
  linkText: 'Privacy Policy',
  href: 'https://athenahealth.com/privacy-rights',
  format: 'other',
  position: 254566,
  context: '"> <a data-link-type="footer" data-link-text="Privacy Policy" data-link-url="https://athenahealth.co...'
}
🎯 [Privacy Policy Detection] Found privacy policy link: {
  url: 'https://athenahealth.com/privacy-rights',
  linkText: 'Privacy Policy',
  href: 'https://athenahealth.com/privacy-rights',
  format: 'other',
  position: 273458,
  context: 's-serif;text-align:center"> <a data-link-type="footer" data-link-text="Privacy Policy" data-link-url...'
}
✅ [Privacy Policy Detection] Link detection complete: {
  totalLinksScanned: 197,
  privacyPolicyLinksFound: 2,
  discoveredUrls: [
    {
      url: 'https://athenahealth.com/privacy-rights',
      text: 'Privacy Policy'
    },
    {
      url: 'https://athenahealth.com/privacy-rights',
      text: 'Privacy Policy'
    }
  ]
}
HipaaOrchestrator: Completed check privacy-policy-presence - PASSED
📄 [Content Extraction] Starting text extraction from HTML
📊 [Content Extraction] Original HTML length: 314318
🧹 [Content Extraction] Found elements to remove: { totalElements:  946, scriptsAndStyles: 24 }
📝 [Content Extraction] Raw text content length: 28488
✅ [Content Extraction] Text extraction complete: {
  originalHtmlLength: 314318,
  rawTextLength: 28488,
  cleanedTextLength: 28394,
  compressionRatio: '91.0%',
  wordCount: 4034,
  lineCount: 1
}
🔍 [Level 1 Analysis] Starting pattern matching analysis
📊 [Level 1 Analysis] Content length: 28394
📝 [Level 1 Analysis] Content preview: Patient LoginathenaOne Login Solutions Solutions Who We Serve Who We Serve Resources Resources Company Company Request a Demo Request a Demo SolutionsOur PlatformathenaOneAn AI-powered, all-in-one sol...
📋 [Level 1 Analysis] Analyzing HIPAA-specific patterns...
✅ [Level 1 Analysis] HIPAA analysis complete: { hipaaMatches: 7, hipaaScore: 56 }
🎯 [Level 1 Analysis] Pattern matching analysis complete: {
  analysisType: 'HIPAA-specific',
  score: 56,
  foundPatterns: 7,
  totalPatterns: 9,
  confidence: 89.44444444444444,
  processingTime: '5ms',
  matchDetails: [
    {
      requirement: 'Notice of Privacy Practices header',
      text: 'Privacy Notice...',
      position: 19652
    },
    {
      requirement: 'Individual rights section',
      text: 'your rights...',
      position: 17593
    },
    {
      requirement: 'Individual rights section',
      text: 'your rights...',
      position: 22848
    },
    {
      requirement: 'Uses and disclosures section',
      text: 'use and disclosure...',
      position: 21702
    },
    {
      requirement: 'Uses and disclosures section',
      text: 'sharing of your personal information.To opt out of...',
      position: 18314
    }
  ]
}
🧠 [Level 2 Analysis] Starting NLP analysis with Compromise.js
📊 [Level 2 Analysis] Text length: 28394
📝 [Level 2 Analysis] Text preview: Patient LoginathenaOne Login Solutions Solutions Who We Serve Who We Serve Resources Resources Company Company Request a Demo Request a Demo SolutionsOur PlatformathenaOneAn AI-powered, all-in-one sol...
🔍 [Level 2 Analysis] Parsing text with Compromise.js...
✅ [Level 2 Analysis] Text parsed successfully
👥 [Level 2 Analysis] Extracting entities...
✅ [Level 2 Analysis] Entity extraction complete: { people: 1, organizations: 27, phoneNumbers: 3, emails: 0 }
🔒 [Level 2 Analysis] Finding privacy statements...
NLP analysis failed: TypeError: sentence.text is not a function
    at D:\Web projects\Comply Checker\backend\src\compliance\hipaa\utils\nlp-analyzer.ts:214:29
    at Array.filter (<anonymous>)
    at Function.findPrivacyStatements (D:\Web projects\Comply Checker\backend\src\compliance\hipaa\utils\nlp-analyzer.ts:213:46)
    at Function.analyzeWithCompromise (D:\Web projects\Comply Checker\backend\src\compliance\hipaa\utils\nlp-analyzer.ts:89:38)
    at async Object.checkHipaaSpecificContent [as function] (D:\Web projects\Comply Checker\backend\src\compliance\hipaa\checks\hipaa-specific-content.ts:143:24)
    at async D:\Web projects\Comply Checker\backend\src\compliance\hipaa\orchestrator.ts:335:26
    at async Promise.all (index 1)
    at async HipaaPrivacyPolicyOrchestrator.executeChecksWithConcurrency (D:\Web projects\Comply Checker\backend\src\compliance\hipaa\orchestrator.ts:358:28)
    at async HipaaPrivacyPolicyOrchestrator.performComprehensiveScan (D:\Web projects\Comply Checker\backend\src\compliance\hipaa\orchestrator.ts:90:28)
    at async ScanService.initiateNewScan (D:\Web projects\Comply Checker\backend\src\services\scan-service.ts:153:33)
🤖 [Level 3 Analysis] Starting AI analysis with DistilBERT
📊 [Level 3 Analysis] Text length: 28394
📝 [Level 3 Analysis] Text preview: Patient LoginathenaOne Login Solutions Solutions Who We Serve Who We Serve Resources Resources Company Company Request a Demo Request a Demo SolutionsOur PlatformathenaOneAn AI-powered, all-in-one sol...
🔧 [Level 3 Analysis] Initializing AI models...
✅ [Level 3 Analysis] AI models initialized successfully
🔍 [Level 3 Analysis] Performing legal compliance assessment...
Text classifier not available - using pattern-based fallback analysis
✅ [Level 3 Analysis] Legal compliance assessment complete: { score: 25, confidence: 70 }
🔍 [Level 3 Analysis] Finding compliance gaps...
✅ [Level 3 Analysis] Gap analysis complete: { gapsFound: 0, criticalGaps: 0, highGaps: 0 }
🔍 [Level 3 Analysis] Assessing privacy risks...
✅ [Level 3 Analysis] Risk assessment complete: { risksFound: 3, highRisks: 2 }
💡 [Level 3 Analysis] Generating AI recommendations...
✅ [Level 3 Analysis] Recommendations generated: 4
🎯 [Level 3 Analysis] Detecting positive findings...
✅ [Level 3 Analysis] Positive findings detected: 7
📋 [Level 3 Analysis] Generating findings...
✅ [Level 3 Analysis] Findings generated: 3
📊 [Level 3 Analysis] Calculating overall compliance score...
📊 [Level 3 Analysis] Calculating confidence score...
🎯 [Level 3 Analysis] AI analysis complete: {
  score: 15,
  confidence: 85,
  processingTime: '27ms',
  gapsSummary: { total: 0, critical: 0, high: 0, medium: 0 },
  risksSummary: { total: 3, high: 2, types: [ 'privacy', 'security' ] },
  recommendationsSummary: { total: 4, highPriority: 2 },
  findingsSummary: { total: 3, types: [ 'risk_factor' ] }
}
🔢 [Score Calculation] Level combination details: {
  level1Score: 56,
  level1Weight: 0.3,
  level1Contribution: 16.8,
  level2Score: 0,
  level2Weight: 0.35,
  level2Contribution: 0,
  level3Score: 15,
  level3Weight: 0.35,
  level3Contribution: 5.25,
  totalScore: 22.05,
  totalWeight: 0.9999999999999999,
  finalScore: 22
}
HipaaOrchestrator: Completed check hipaa-specific-content - FAILED
📄 [Content Extraction] Starting text extraction from HTML
📊 [Content Extraction] Original HTML length: 314318
🧹 [Content Extraction] Found elements to remove: { totalElements:  946, scriptsAndStyles: 24 }
📝 [Content Extraction] Raw text content length: 28488
✅ [Content Extraction] Text extraction complete: {
  originalHtmlLength: 314318,
  rawTextLength: 28488,
  cleanedTextLength: 28394,
  compressionRatio: '91.0%',
  wordCount: 4034,
  lineCount: 1
}
HipaaOrchestrator: Completed check contact-information - FAILED
📊 [Frontend Result Generation] Calculating summary statistics...
🏥 [Industry Detection] URL: https://www.athenahealth.com/ -> healthcare
🔢 [Improved Scoring] Calculation details: {
  industryType: 'healthcare',
  thresholds: { passing: 75, good: 85, excellent: 95, critical: 40 },
  confidenceWeighting: false,
  calculations: {
    totalWeightedScore: 27.4,
    totalWeight: 1,
    totalConfidence: 168,
    confidenceCount: 3,
    averageConfidence: 56
  }
}
✅ [Frontend Result Generation] Improved scoring applied: {
  industryType: 'healthcare',
  oldScoringWouldBe: 57,
  newOverallScore: 27,
  overallPassed: false,
  complianceLevel: 'non_compliant',
  totalChecks: 3,
  passedChecks: 1,
  failedChecks: 2,
  scoringBreakdown: [
    { checkId: 'HIPAA-PP-001', weight: 0.15, contribution: '12.0' },
    {
      checkId: 'HIPAA-COMPREHENSIVE-001',
      weight: 0.7,
      contribution: '15.4'
    },
    { checkId: 'HIPAA-CONTACT-001', weight: 0.15, contribution: '0.0' }
  ]
}
🔍 [Frontend Result Generation] Extracting analysis levels...
✅ [Frontend Result Generation] Analysis levels extracted: [ 1, 2, 3 ]
💡 [Frontend Result Generation] Generating recommendations...
✅ [Frontend Result Generation] Recommendations generated: {
  count: 6,
  priorities: [
    {
      title: 'Address Comprehensive HIPAA Privacy Policy Analysis Issues',
      priority: 1
    },
    { title: 'Mitigate Privacy Risk', priority: 2 },
    { title: 'Mitigate Security Risk', priority: 2 },
    {
      title: 'Address Privacy Officer Contact Information Issues',
      priority: 2
    },
    { title: 'Review and Update Privacy Policy', priority: 3 },
    { title: 'Mitigate Privacy Risk', priority: 4 }
  ]
}
✅ [Frontend Result Generation] Compliance level from improved scoring: non_compliant
🎯 [Frontend Result Generation] Enhanced HIPAA scan completed with 27% compliance score
🎯 [Frontend Result Generation] Final enhanced HIPAA result generated: {
  scanId: 'hipaa-1750008997249-a5cnw04t0',
  targetUrl: 'https://www.athenahealth.com/',
  overallScore: 27,
  overallPassed: false,
  complianceLevel: 'non_compliant',
  processingTime: '7405ms',
  checksExecuted: 3,
  analysisLevelsUsed: [ 1, 2, 3 ],
  recommendationsGenerated: 6,
  issuesSummary: { critical: 1, high: 1, medium: 0, low: 0 },
  levelBreakdown: [
    {
      checkId: 'HIPAA-PP-001',
      name: 'Privacy Policy Presence',
      passed: true,
      score: undefined,
      levelsUsed: [Array]
    },
    {
      checkId: 'HIPAA-COMPREHENSIVE-001',
      name: 'Comprehensive HIPAA Privacy Policy Analysis',
      passed: false,
      score: 22,
      levelsUsed: [Array]
    },
    {
      checkId: 'HIPAA-CONTACT-001',
      name: 'Privacy Officer Contact Information',
      passed: false,
      score: undefined,
      levelsUsed: [Array]
    }
  ]
}
🎯 [Frontend Result Generation] Enhanced HIPAA scan hipaa-1750008997249-a5cnw04t0 completed in 7405ms
ScanService: Enhanced HIPAA scan completed with 3 checks and 27% compliance score
ScanService: Saving enhanced HIPAA results to database for scan 3eb9de85-e31c-484f-b308-685e5dbd27d2...
💾 [Database Storage] Starting enhanced HIPAA results storage
📊 [Database Storage] Scan details: {
  scanId: '3eb9de85-e31c-484f-b308-685e5dbd27d2',
  targetUrl: 'https://www.athenahealth.com/',
  overallScore: 27,
  overallPassed: false,
  checksCount: 3,
  recommendationsCount: 6,
  processingTime: '7405ms'
}
🔄 [Database Storage] Starting database transaction...
📝 [Database Storage] Inserting main HIPAA scan record...
✅ [Database Storage] Main scan record saved with ID: 1a0a459e-f5c8-45a8-aa56-f5201844278c
📋 [Database Storage] Saving check results...
📝 [Database Storage] Saving check 1/3: Privacy Policy Presence
📝 [Database Storage] Saving check 2/3: Comprehensive HIPAA Privacy Policy Analysis
📝 [Database Storage] Saving check 3/3: Privacy Officer Contact Information
✅ [Database Storage] All check results saved
💡 [Database Storage] Saving recommendations...
📝 [Database Storage] Saving recommendation 1/6: Address Comprehensive HIPAA Privacy Policy Analysis Issues
📝 [Database Storage] Saving recommendation 2/6: Mitigate Privacy Risk
📝 [Database Storage] Saving recommendation 3/6: Mitigate Security Risk
📝 [Database Storage] Saving recommendation 4/6: Address Privacy Officer Contact Information Issues
📝 [Database Storage] Saving recommendation 5/6: Review and Update Privacy Policy
📝 [Database Storage] Saving recommendation 6/6: Mitigate Privacy Risk
✅ [Database Storage] All recommendations saved
🔄 [Database Storage] Committing transaction...
🎯 [Database Storage] Enhanced HIPAA results storage complete: {
  hipaaScanId: '1a0a459e-f5c8-45a8-aa56-f5201844278c',
  scanId: '3eb9de85-e31c-484f-b308-685e5dbd27d2',
  checksStored: 3,
  recommendationsStored: 6,
  totalFindings: 19
}
ScanService: Enhanced HIPAA results saved with hipaa_scan_id: 1a0a459e-f5c8-45a8-aa56-f5201844278c
ScanService: Verifying enhanced HIPAA results storage for scan 3eb9de85-e31c-484f-b308-685e5dbd27d2...
🔍 [DEBUG] getLevelResults for checkResultId: 2887b761-1249-49ce-bc51-b3ed8722baaf, found 0 records
🔍 [DEBUG] Final levelResults keys: []
🔍 [DEBUG] getLevelResults for checkResultId: 4800a025-e707-4ba5-9a77-d7becf41bb1f, found 3 records
🔍 [DEBUG] Level 3 record raw data: {
  id: '412c46e3-c629-4d4a-ab01-8560feb6a88c',
  positive_findings: 'HAS DATA',
  ai_recommendations: 'HAS DATA',
  risk_factors: 'HAS DATA',
  compliance_gaps: 'HAS DATA'
}
🔍 [DEBUG] Level 3 parsed data: {
  positiveFindings: 0,
  recommendations: 0,
  riskFactors: 0,
  identifiedGaps: 0
}
🔍 [DEBUG] Final levelResults keys: [ 'level1', 'level2', 'level3' ]
🔍 [DEBUG] getLevelResults for checkResultId: 48ec4a9e-49b8-4d54-b559-4e7c6abd1af9, found 0 records
🔍 [DEBUG] Final levelResults keys: []
ScanService: Enhanced HIPAA results verification successful - 3 checks stored
ScanService: Inserting 3 findings for scan 3eb9de85-e31c-484f-b308-685e5dbd27d2
ScanService: Scan 3eb9de85-e31c-484f-b308-685e5dbd27d2 status updated to COMPLETED.
ScanService: Scan 3eb9de85-e31c-484f-b308-685e5dbd27d2 completed successfully.
🔧 [DIRECT FIX] Always attempting to retrieve enhanced HIPAA results for scan: 3eb9de85-e31c-484f-b308-685e5dbd27d2
🔧 [DIRECT FIX] HIPAA scan record query result: {
  found: true,
  scanId: '3eb9de85-e31c-484f-b308-685e5dbd27d2',
  hipaaScanRecord: {
    id: '1a0a459e-f5c8-45a8-aa56-f5201844278c',
    overall_score: '27.00',
    compliance_level: 'non_compliant',
    total_checks: 3,
    created_at: 2025-06-15T17:36:44.719Z
  }
}
🔧 [DIRECT FIX] Found HIPAA scan record, retrieving enhanced results...
🔍 [DEBUG] getLevelResults for checkResultId: 2887b761-1249-49ce-bc51-b3ed8722baaf, found 0 records
🔍 [DEBUG] Final levelResults keys: []
🔍 [DEBUG] getLevelResults for checkResultId: 4800a025-e707-4ba5-9a77-d7becf41bb1f, found 3 records
🔍 [DEBUG] Level 3 record raw data: {
  id: '412c46e3-c629-4d4a-ab01-8560feb6a88c',
  positive_findings: 'HAS DATA',
  ai_recommendations: 'HAS DATA',
  risk_factors: 'HAS DATA',
  compliance_gaps: 'HAS DATA'
}
🔍 [DEBUG] Level 3 parsed data: {
  positiveFindings: 0,
  recommendations: 0,
  riskFactors: 0,
  identifiedGaps: 0
}
🔍 [DEBUG] Final levelResults keys: [ 'level1', 'level2', 'level3' ]
🔍 [DEBUG] getLevelResults for checkResultId: 48ec4a9e-49b8-4d54-b559-4e7c6abd1af9, found 0 records
🔍 [DEBUG] Final levelResults keys: []
🎯 [DIRECT FIX] SUCCESS! Enhanced HIPAA results retrieved: {
  overallScore: '27.00',
  checksCount: 3,
  recommendationsCount: 6,
  complianceLevel: 'non_compliant'
}
🌐 [Frontend API Response] Preparing scan response for frontend: {
  scanId: '3eb9de85-e31c-484f-b308-685e5dbd27d2',
  url: 'https://www.athenahealth.com/',
  status: 'completed',
  hasEnhancedResults: true,
  enhancedResultsStructure: {
    overallScore: '27.00',
    overallPassed: false,
    checksCount: 3,
    recommendationsCount: 6,
    complianceLevel: 'non_compliant',
    analysisLevelsUsed: [],
    processingTime: 7405
  },
  basicFindingsCount: 3,
  scanTimestamp: 2025-06-15T17:36:37.285Z
}
🔍 [API Debug] Processing check: HIPAA-PP-001
🔍 [API Debug] Check levelResults: {}
🔍 [API Debug] Check levelResults type: object
🔍 [API Debug] Check levelResults keys: []
🔍 [API Debug] Check levelResults JSON: {}
🔍 [API Debug] Final analysisLevels for HIPAA-PP-001: []
🔍 [API Debug] Final analysisLevels JSON: []
🔍 [API Debug] Processing check: HIPAA-COMPREHENSIVE-001
🔍 [API Debug] Check levelResults: {
  level1: {
    level: 1,
    method: 'HIPAA-specific Pattern Matching',
    score: '56.00',
    confidence: '89.00',
    processingTime: 5,
    foundPatterns: 7,
    totalPatterns: 9,
    findings: []
  },
  level2: {
    level: 2,
    method: 'NLP with Compromise.js',
    score: '0.00',
    confidence: '0.00',
    processingTime: 455,
    entities: {},
    privacyStatements: [],
    rightsStatements: [],
    findings: []
  },
  level3: {
    level: 3,
    method: 'AI Analysis with DistilBERT',
    score: '15.00',
    confidence: '85.00',
    processingTime: 27,
    identifiedGaps: [],
    riskFactors: [],
    recommendations: [],
    findings: [],
    positiveFindings: []
  }
}
🔍 [API Debug] Check levelResults type: object
🔍 [API Debug] Check levelResults keys: [ 'level1', 'level2', 'level3' ]
🔍 [API Debug] Check levelResults JSON: {
  "level1": {
    "level": 1,
    "method": "HIPAA-specific Pattern Matching",
    "score": "56.00",
    "confidence": "89.00",
    "processingTime": 5,
    "foundPatterns": 7,
    "totalPatterns": 9,
    "findings": []
  },
  "level2": {
    "level": 2,
    "method": "NLP with Compromise.js",
    "score": "0.00",
    "confidence": "0.00",
    "processingTime": 455,
    "entities": {},
    "privacyStatements": [],
    "rightsStatements": [],
    "findings": []
  },
  "level3": {
    "level": 3,
    "method": "AI Analysis with DistilBERT",
    "score": "15.00",
    "confidence": "85.00",
    "processingTime": 27,
    "identifiedGaps": [],
    "riskFactors": [],
    "recommendations": [],
    "findings": [],
    "positiveFindings": []
  }
}
🔍 [API Debug] Processing level 1: {
  level: 1,
  method: 'HIPAA-specific Pattern Matching',
  score: '56.00',
  confidence: '89.00',
  processingTime: 5,
  foundPatterns: 7,
  totalPatterns: 9,
  findings: []
}
🔍 [API Debug] Level score type: string value: 56.00
🔍 [API Debug] Processing level 2: {
  level: 2,
  method: 'NLP with Compromise.js',
  score: '0.00',
  confidence: '0.00',
  processingTime: 455,
  entities: {},
  privacyStatements: [],
  rightsStatements: [],
  findings: []
}
🔍 [API Debug] Level score type: string value: 0.00
🔍 [API Debug] Processing level 3: {
  level: 3,
  method: 'AI Analysis with DistilBERT',
  score: '15.00',
  confidence: '85.00',
  processingTime: 27,
  identifiedGaps: [],
  riskFactors: [],
  recommendations: [],
  findings: [],
  positiveFindings: []
}
🔍 [API Debug] Level score type: string value: 15.00
🔍 [API Debug] Final analysisLevels for HIPAA-COMPREHENSIVE-001: [
  {
    level: 1,
    method: 'HIPAA-specific Pattern Matching',
    score: '56.00',
    confidence: '89.00',
    processingTime: 5,
    findings: [],
    foundPatterns: 7,
    totalPatterns: 9
  },
  {
    level: 2,
    method: 'NLP with Compromise.js',
    score: '0.00',
    confidence: '0.00',
    processingTime: 455,
    findings: [],
    entities: {},
    privacyStatements: [],
    rightsStatements: []
  },
  {
    level: 3,
    method: 'AI Analysis with DistilBERT',
    score: '15.00',
    confidence: '85.00',
    processingTime: 27,
    findings: [],
    identifiedGaps: [],
    riskFactors: [],
    recommendations: [],
    positiveFindings: []
  }
]
🔍 [API Debug] Final analysisLevels JSON: [
  {
    "level": 1,
    "method": "HIPAA-specific Pattern Matching",
    "score": "56.00",
    "confidence": "89.00",
    "processingTime": 5,
    "findings": [],
    "foundPatterns": 7,
    "totalPatterns": 9
  },
  {
    "level": 2,
    "method": "NLP with Compromise.js",
    "score": "0.00",
    "confidence": "0.00",
    "processingTime": 455,
    "findings": [],
    "entities": {},
    "privacyStatements": [],
    "rightsStatements": []
  },
  {
    "level": 3,
    "method": "AI Analysis with DistilBERT",
    "score": "15.00",
    "confidence": "85.00",
    "processingTime": 27,
    "findings": [],
    "identifiedGaps": [],
    "riskFactors": [],
    "recommendations": [],
    "positiveFindings": []
  }
]
🔍 [API Debug] Processing check: HIPAA-CONTACT-001
🔍 [API Debug] Check levelResults: {}
🔍 [API Debug] Check levelResults type: object
🔍 [API Debug] Check levelResults keys: []
🔍 [API Debug] Check levelResults JSON: {}
🔍 [API Debug] Final analysisLevels for HIPAA-CONTACT-001: []
🔍 [API Debug] Final analysisLevels JSON: []
📊 [Frontend API Response] Enhanced HIPAA results details: {
  targetUrl: 'https://www.athenahealth.com/',
  overallScore: '27.00',
  overallPassed: false,
  summary: {
    totalChecks: 3,
    passedChecks: 1,
    failedChecks: 2,
    complianceLevel: 'non_compliant',
    analysisLevelsUsed: []
  },
  checksBreakdown: [
    {
      checkId: 'HIPAA-PP-001',
      name: 'Privacy Policy Presence',
      passed: true,
      score: null,
      confidence: '80.00',
      analysisLevels: []
    },
    {
      checkId: 'HIPAA-COMPREHENSIVE-001',
      name: 'Comprehensive HIPAA Privacy Policy Analysis',
      passed: false,
      score: '22.00',
      confidence: '58.00',
      analysisLevels: [Array]
    },
    {
      checkId: 'HIPAA-CONTACT-001',
      name: 'Privacy Officer Contact Information',
      passed: false,
      score: null,
      confidence: '30.00',
      analysisLevels: []
    }
  ],
  recommendationsCount: 6,
  metadata: { processingTime: 7405, version: '2.0', analysisLevelsUsed: [] }
}
🚀 [FINAL RESPONSE] Complete enhanced HIPAA response being sent to frontend:
{
  "enhancedHipaaResults": {
    "targetUrl": "https://www.athenahealth.com/",
    "overallScore": "27.00",
    "checksBreakdown": [
      {
        "checkId": "HIPAA-PP-001",
        "name": "Privacy Policy Presence",
        "analysisLevels": []
      },
      {
        "checkId": "HIPAA-COMPREHENSIVE-001",
        "name": "Comprehensive HIPAA Privacy Policy Analysis",
        "analysisLevels": [
          {
            "level": 1,
            "method": "HIPAA-specific Pattern Matching",
            "score": "56.00",
            "confidence": "89.00",
            "processingTime": 5,
            "foundPatterns": 7,
            "totalPatterns": 9,
            "findings": []
          },
          {
            "level": 2,
            "method": "NLP with Compromise.js",
            "score": "0.00",
            "confidence": "0.00",
            "processingTime": 455,
            "entities": {},
            "privacyStatements": [],
            "rightsStatements": [],
            "findings": []
          },
          {
            "level": 3,
            "method": "AI Analysis with DistilBERT",
            "score": "15.00",
            "confidence": "85.00",
            "processingTime": 27,
            "identifiedGaps": [],
            "riskFactors": [],
            "recommendations": [],
            "findings": [],
            "positiveFindings": []
          }
        ]
      },
      {
        "checkId": "HIPAA-CONTACT-001",
        "name": "Privacy Officer Contact Information",
        "analysisLevels": []
      }
    ]
  }
}
2025-06-15T17:36:45.104Z [INFO] - Scan retrieval debug info: - {"scanId":"3eb9de85-e31c-484f-b308-685e5dbd27d2","hasEnhancedResults":true,"enhancedResultsKeys":["targetUrl","timestamp","overallScore","overallPassed","summary","checks","recommendations","metadata"],"scanStatus":"completed","findingsCount":3}
