#!/usr/bin/env pwsh
# PowerShell script to fix Docker Desktop "com.docker.build: exit status 1" error

Write-Host "🔧 Docker Desktop Error Fix Script" -ForegroundColor Red
Write-Host "==================================" -ForegroundColor Red
Write-Host ""
Write-Host "Error: 'running services: running com.docker.build: exit status 1'" -ForegroundColor Yellow
Write-Host "This error typically occurs due to corrupted Docker data or configuration." -ForegroundColor Yellow
Write-Host ""

# Function to stop Docker processes
function Stop-DockerProcesses {
    Write-Host "🛑 Stopping all Docker processes..." -ForegroundColor Yellow
    
    $processes = @(
        "Docker Desktop",
        "com.docker.cli",
        "com.docker.backend", 
        "com.docker.proxy",
        "dockerd",
        "vpnkit",
        "com.docker.service"
    )
    
    foreach ($process in $processes) {
        try {
            Get-Process -Name $process -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
            Write-Host "  ✅ Stopped $process" -ForegroundColor Green
        } catch {
            Write-Host "  ⚠️ $process not running" -ForegroundColor Gray
        }
    }
}

# Function to stop Docker services
function Stop-DockerServices {
    Write-Host "🛑 Stopping Docker services..." -ForegroundColor Yellow
    
    $services = @(
        "Docker Desktop Service",
        "com.docker.service"
    )
    
    foreach ($service in $services) {
        try {
            Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
            Write-Host "  ✅ Stopped $service" -ForegroundColor Green
        } catch {
            Write-Host "  ⚠️ $service not running" -ForegroundColor Gray
        }
    }
}

# Function to clear Docker data
function Clear-DockerData {
    Write-Host "🗑️ Clearing Docker Desktop data..." -ForegroundColor Yellow
    Write-Host "⚠️ This will reset Docker Desktop to default settings" -ForegroundColor Red
    
    $confirmation = Read-Host "Continue? (y/N)"
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-Host "Operation cancelled" -ForegroundColor Yellow
        return $false
    }
    
    $paths = @(
        "$env:APPDATA\Docker",
        "$env:LOCALAPPDATA\Docker", 
        "$env:PROGRAMDATA\Docker",
        "$env:PROGRAMDATA\DockerDesktop",
        "$env:USERPROFILE\.docker"
    )
    
    foreach ($path in $paths) {
        if (Test-Path $path) {
            try {
                Remove-Item -Path $path -Recurse -Force -ErrorAction SilentlyContinue
                Write-Host "  ✅ Cleared $path" -ForegroundColor Green
            } catch {
                Write-Host "  ❌ Failed to clear $path" -ForegroundColor Red
            }
        } else {
            Write-Host "  ⚠️ $path not found" -ForegroundColor Gray
        }
    }
    
    return $true
}

# Function to reset network
function Reset-NetworkStack {
    Write-Host "🌐 Resetting network stack..." -ForegroundColor Yellow
    
    try {
        netsh winsock reset | Out-Null
        netsh int ip reset | Out-Null
        Write-Host "  ✅ Network stack reset" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ Failed to reset network stack" -ForegroundColor Red
    }
}

# Function to check Windows features
function Test-WindowsFeatures {
    Write-Host "🔍 Checking Windows features..." -ForegroundColor Yellow
    
    # Check Hyper-V
    try {
        $hyperV = Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All -ErrorAction SilentlyContinue
        if ($hyperV -and $hyperV.State -eq "Enabled") {
            Write-Host "  ✅ Hyper-V is enabled" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️ Hyper-V not enabled (will use WSL2)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "  ⚠️ Cannot check Hyper-V status" -ForegroundColor Yellow
    }
    
    # Check Containers feature
    try {
        $containers = Get-WindowsOptionalFeature -Online -FeatureName Containers -ErrorAction SilentlyContinue
        if ($containers -and $containers.State -eq "Enabled") {
            Write-Host "  ✅ Containers feature is enabled" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️ Containers feature not enabled" -ForegroundColor Yellow
            Write-Host "  Enabling Containers feature..." -ForegroundColor Yellow
            Enable-WindowsOptionalFeature -Online -FeatureName Containers -All -NoRestart
        }
    } catch {
        Write-Host "  ❌ Failed to check/enable Containers feature" -ForegroundColor Red
    }
}

# Function to find Docker Desktop
function Find-DockerDesktop {
    $paths = @(
        "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe",
        "${env:ProgramFiles(x86)}\Docker\Docker\Docker Desktop.exe",
        "${env:LOCALAPPDATA}\Programs\Docker\Docker\Docker Desktop.exe"
    )
    
    foreach ($path in $paths) {
        if (Test-Path $path) {
            return $path
        }
    }
    return $null
}

# Main execution
Write-Host "Starting Docker Desktop error fix process..." -ForegroundColor Cyan

# Step 1: Stop everything
Stop-DockerProcesses
Stop-DockerServices

# Step 2: Clear data
if (-not (Clear-DockerData)) {
    Write-Host "❌ Cannot proceed without clearing data" -ForegroundColor Red
    exit 1
}

# Step 3: Reset network
Reset-NetworkStack

# Step 4: Check Windows features
Test-WindowsFeatures

# Step 5: Find and start Docker Desktop
Write-Host "🔍 Looking for Docker Desktop..." -ForegroundColor Yellow
$dockerPath = Find-DockerDesktop

if (-not $dockerPath) {
    Write-Host "❌ Docker Desktop not found!" -ForegroundColor Red
    Write-Host "Please reinstall Docker Desktop from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Found Docker Desktop at: $dockerPath" -ForegroundColor Green

# Step 6: Start Docker Desktop
Write-Host "🚀 Starting Docker Desktop..." -ForegroundColor Yellow
try {
    Start-Process -FilePath $dockerPath -WindowStyle Normal
    Write-Host "✅ Docker Desktop started" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start Docker Desktop: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 7: Wait for Docker to be ready
Write-Host "⏳ Waiting for Docker Desktop to initialize..." -ForegroundColor Yellow
Write-Host "This may take 2-5 minutes. Please be patient..." -ForegroundColor White
Write-Host ""
Write-Host "👀 Watch your system tray for the Docker whale icon" -ForegroundColor Cyan
Write-Host "   • Animating = still starting" -ForegroundColor White
Write-Host "   • Steady = ready to use" -ForegroundColor White
Write-Host ""

$maxAttempts = 24
$attempt = 0

while ($attempt -lt $maxAttempts) {
    $attempt++
    Write-Host "Attempt $attempt/$maxAttempts : Testing Docker daemon..." -ForegroundColor Gray
    
    try {
        $result = docker version --format "{{.Server.Version}}" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "🎉 Docker Desktop is now running successfully!" -ForegroundColor Green
            break
        }
    } catch {
        # Continue waiting
    }
    
    if ($attempt -eq $maxAttempts) {
        Write-Host "⚠️ Docker Desktop is taking longer than expected" -ForegroundColor Yellow
        break
    }
    
    Start-Sleep -Seconds 15
}

# Final verification
Write-Host ""
Write-Host "🔍 Final verification..." -ForegroundColor Cyan

try {
    $version = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker CLI: $version" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker CLI not responding" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Docker CLI error" -ForegroundColor Red
}

try {
    $containers = docker ps 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker daemon is responding" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker daemon not responding" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Docker daemon error" -ForegroundColor Red
}

Write-Host ""
Write-Host "📋 Summary:" -ForegroundColor Cyan

if ($LASTEXITCODE -eq 0) {
    Write-Host "🎉 Docker Desktop fix completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "You can now run your Docker services:" -ForegroundColor White
    Write-Host "  cd `"D:\Web projects\Comply Checker`"" -ForegroundColor Gray
    Write-Host "  docker-compose up -d" -ForegroundColor Gray
} else {
    Write-Host "⚠️ Docker Desktop may still be starting or needs additional fixes" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔧 Additional troubleshooting options:" -ForegroundColor Cyan
    Write-Host "1. Wait longer (first startup can take 5+ minutes)" -ForegroundColor White
    Write-Host "2. Restart your computer" -ForegroundColor White
    Write-Host "3. Reinstall Docker Desktop completely" -ForegroundColor White
    Write-Host "4. Check BIOS virtualization settings" -ForegroundColor White
    Write-Host "5. Update Windows to latest version" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to continue"
