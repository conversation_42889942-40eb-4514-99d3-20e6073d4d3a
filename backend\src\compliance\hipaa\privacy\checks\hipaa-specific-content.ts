// backend/src/compliance/hipaa/privacy/checks/hipaa-specific-content.ts

/**
 * HIPAA-Specific Content Analysis (3-Level Analysis Hub)
 * Integrates all three levels of analysis for comprehensive HIPAA compliance validation
 */

import {
  HipaaCheckResult,
  HipaaCheckCategory,
  HipaaSeverity,
  HipaaFindingType,
  HipaaFinding,
  CheckOptions,
  Level1Result,
  Level2Result,
  Level3Result,
  HipaaRemediation,
  HipaaEvidence,
} from '../types';
import { URLResolver } from '../utils/url-resolver';
import { ContentAnalyzer } from '../utils/content-analyzer';
import { PatternMatcher } from '../utils/pattern-matcher';
import { NLPAnalyzer } from '../utils/nlp-analyzer';
import { AIAnalyzer } from '../utils/ai-analyzer';
import { ReadabilityScorer } from '../utils/readability-scorer';
import { ANALYSIS_WEIGHTS } from '../constants';

/**
 * Comprehensive HIPAA-specific content analysis using 3-level approach
 * @param targetUrl - The URL to analyze for HIPAA compliance
 * @param options - Configuration options for the analysis
 * @returns Promise<HipaaCheckResult> - Detailed analysis result
 */
export async function checkHipaaSpecificContent(
  targetUrl: string,
  options: CheckOptions = {},
): Promise<HipaaCheckResult> {
  const startTime = Date.now();

  const result: HipaaCheckResult = {
    checkId: 'HIPAA-COMPREHENSIVE-001',
    name: 'Comprehensive HIPAA Privacy Policy Analysis',
    category: HipaaCheckCategory.HIPAA_SPECIFIC,
    passed: false,
    severity: HipaaSeverity.MEDIUM,
    confidence: 0,
    description:
      'Comprehensive 3-level analysis of HIPAA privacy policy compliance using pattern matching, NLP, and AI analysis',
    details: {
      summary: '',
      findings: [],
      metrics: {
        processingTime: 0,
        contentLength: 0,
        sectionsFound: 0,
        patternsMatched: 0,
        entitiesExtracted: 0,
        readabilityScore: 0,
      },
      context: {
        url: targetUrl,
        pageTitle: '',
        lastModified: '',
        contentType: '',
        language: '',
      },
    },
    remediation: {
      priority: 'high',
      effort: 'moderate',
      steps: [],
      resources: [],
      timeline: '2-3 weeks',
      estimatedCost: '$2,000 - $5,000 for legal review',
    },
    evidence: [],
    metadata: {
      checkVersion: '2.0',
      processingTime: 0,
      analysisLevels: [1, 2, 3],
      warnings: [],
    },
    levelResults: {},
    overallScore: 0,
  };

  let level1Result: Level1Result | null = null;
  let level2Result: Level2Result | null = null;
  let level3Result: Level3Result | null = null;

  try {
    // Step 1: Discover and fetch privacy policy content
    const privacyPolicyUrl = await discoverPrivacyPolicyUrl(targetUrl);
    const htmlContent = await URLResolver.fetchPageContent(privacyPolicyUrl, {
      timeout: options.timeout || 30000,
      userAgent: options.userAgent,
    });

    // Step 2: Extract and clean content
    const cleanText = ContentAnalyzer.extractText(htmlContent);
    const sections = ContentAnalyzer.findSections(cleanText);
    const pageTitle = ContentAnalyzer.extractPageTitle(htmlContent);
    const language = ContentAnalyzer.detectLanguage(htmlContent);

    // Update context information
    result.details.context.pageTitle = pageTitle;
    result.details.context.language = language;
    result.details.context.contentType = 'text/html';
    result.details.metrics.contentLength = cleanText.length;
    result.details.metrics.sectionsFound = sections.length;

    // Step 3: Level 1 Analysis - Basic Pattern Matching
    if (options.enableLevel1 !== false) {
      try {
        level1Result = PatternMatcher.performLevel1Analysis(cleanText);
        (result.levelResults = result.levelResults || {}).level1 = level1Result;
        result.details.metrics.patternsMatched = level1Result.foundPatterns;

        // Add Level 1 findings
        result.details.findings.push(
          ...level1Result.findings.map((finding) => ({
            type: finding.type as HipaaFindingType,
            location: finding.location.toString(),
            content: finding.content,
            severity: finding.severity as HipaaSeverity,
            message: `Pattern matching: ${finding.requirement}`,
            suggestion: getPatternSuggestion(finding.requirement),
            context: `Level 1 Analysis - ${finding.pattern}`,
            confidence: 95,
          })),
        );
      } catch (error) {
        (result.metadata.warnings = result.metadata.warnings || []).push(
          `Level 1 analysis failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        );
      }
    }

    // Step 4: Level 2 Analysis - NLP with Compromise.js
    if (options.enableLevel2 !== false) {
      try {
        level2Result = await NLPAnalyzer.analyzeWithCompromise(cleanText);
        (result.levelResults = result.levelResults || {}).level2 = level2Result;
        result.details.metrics.entitiesExtracted = Object.values(
          level2Result.entities,
        ).flat().length;

        // Add Level 2 findings
        result.details.findings.push(
          ...level2Result.findings.map((finding) => ({
            type: HipaaFindingType.CONCEPT_MATCH,
            location: 'Content analysis',
            content: finding.content,
            severity: HipaaSeverity.MEDIUM,
            message: `NLP analysis: ${finding.interpretation}`,
            suggestion: getNLPSuggestion(finding.type),
            context: `Level 2 Analysis - ${finding.context || 'NLP processing'}`,
            confidence: finding.confidence || 80,
          })),
        );
      } catch (error) {
        (result.metadata.warnings = result.metadata.warnings || []).push(
          `Level 2 analysis failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        );
      }
    }

    // Step 5: Level 3 Analysis - AI with DistilBERT
    if (options.enableLevel3 !== false) {
      try {
        level3Result = await AIAnalyzer.analyzeWithDistilBERT(cleanText);
        (result.levelResults = result.levelResults || {}).level3 = level3Result;

        // Add Level 3 findings
        result.details.findings.push(
          ...level3Result.findings.map((finding) => ({
            type: finding.type as HipaaFindingType,
            location: 'AI analysis',
            content: finding.content,
            severity: finding.severity as HipaaSeverity,
            message: `AI analysis: ${finding.analysis}`,
            suggestion: finding.recommendation,
            context: `Level 3 Analysis - ${finding.legalBasis || 'AI processing'}`,
            confidence: finding.confidence || 75,
          })),
        );
      } catch (error) {
        (result.metadata.warnings = result.metadata.warnings || []).push(
          `Level 3 analysis failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        );
      }
    }

    // Step 6: Readability Analysis
    const readabilityResult = ReadabilityScorer.assessPlainLanguage(cleanText);
    result.details.metrics.readabilityScore = readabilityResult.score;

    if (readabilityResult.score < 60) {
      result.details.findings.push({
        type: HipaaFindingType.READABILITY_ISSUE,
        location: 'Document readability',
        content: `Readability score: ${readabilityResult.score}`,
        severity: HipaaSeverity.MEDIUM,
        message: 'Privacy policy may be difficult for patients to understand',
        suggestion: 'Simplify language and use shorter sentences for better readability',
        context: `Reading level: ${readabilityResult.readingLevel}`,
        confidence: 90,
      });
    }

    // Step 7: Calculate Combined Score and Results
    const combinedResults = calculateCombinedResults(
      level1Result,
      level2Result,
      level3Result,
      readabilityResult,
    );

    result.overallScore = combinedResults.score;
    result.passed = combinedResults.passed;
    result.confidence = combinedResults.confidence;
    result.severity = combinedResults.severity;

    // Step 8: Generate Comprehensive Remediation
    result.remediation = generateComprehensiveRemediation(
      level1Result,
      level2Result,
      level3Result,
      result.details.findings,
    );

    // Step 9: Collect Evidence
    result.evidence = collectComprehensiveEvidence(
      level1Result,
      level2Result,
      level3Result,
      htmlContent,
    );

    // Step 10: Generate Summary
    result.details.summary = generateComprehensiveSummary(result, combinedResults);
  } catch (error) {
    // Handle errors gracefully
    result.passed = false;
    result.confidence = 0;
    result.severity = HipaaSeverity.CRITICAL;
    result.details.summary = `Error during comprehensive HIPAA analysis: ${error instanceof Error ? error.message : 'Unknown error occurred'}`;
    result.details.findings.push({
      type: HipaaFindingType.ERROR,
      location: 'Analysis execution',
      content: error instanceof Error ? error.message : 'Unknown error occurred',
      severity: HipaaSeverity.CRITICAL,
      message: 'Failed to complete comprehensive HIPAA analysis',
      suggestion: 'Verify privacy policy URL accessibility and content format',
      context: `Error: ${error instanceof Error ? error.name : 'Error'}`,
      confidence: 100,
    });
    (result.metadata.warnings = result.metadata.warnings || []).push(
      `Comprehensive analysis failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
    );
  }

  // Final processing
  const processingTime = Date.now() - startTime;
  result.details.metrics.processingTime = processingTime;
  result.metadata.processingTime = processingTime;

  return result;
}

/**
 * Discover the privacy policy URL from the main website
 */
async function discoverPrivacyPolicyUrl(targetUrl: string): Promise<string> {
  try {
    const privacyUrls = await URLResolver.findPrivacyPolicyUrls(targetUrl);

    if (privacyUrls.length > 0) {
      // Prefer HIPAA-specific privacy notices
      const hipaaUrl = privacyUrls.find(
        (url) =>
          url.toLowerCase().includes('hipaa') ||
          url.toLowerCase().includes('notice-of-privacy-practices'),
      );

      return hipaaUrl || privacyUrls[0];
    }

    // Fallback to main page if no privacy policy found
    return targetUrl;
  } catch (error) {
    // Fallback to main page on error
    return targetUrl;
  }
}

/**
 * Calculate combined results from all analysis levels
 */
function calculateCombinedResults(
  level1: Level1Result | null,
  level2: Level2Result | null,
  level3: Level3Result | null,
  _readability: { score: number; readingLevel: string } | null,
): { score: number; passed: boolean; confidence: number; severity: HipaaSeverity } {
  let totalScore = 0;
  let totalWeight = 0;
  let totalConfidence = 0;
  let confidenceCount = 0;

  // Level 1 contribution
  if (level1) {
    totalScore += level1.score * ANALYSIS_WEIGHTS.LEVEL_1_WEIGHT;
    totalWeight += ANALYSIS_WEIGHTS.LEVEL_1_WEIGHT;
    totalConfidence += level1.confidence || 90;
    confidenceCount++;
  }

  // Level 2 contribution
  if (level2) {
    totalScore += level2.score * ANALYSIS_WEIGHTS.LEVEL_2_WEIGHT;
    totalWeight += ANALYSIS_WEIGHTS.LEVEL_2_WEIGHT;
    totalConfidence += level2.confidence;
    confidenceCount++;
  }

  // Level 3 contribution
  if (level3) {
    totalScore += level3.score * ANALYSIS_WEIGHTS.LEVEL_3_WEIGHT;
    totalWeight += ANALYSIS_WEIGHTS.LEVEL_3_WEIGHT;
    totalConfidence += level3.confidence;
    confidenceCount++;
  }

  // REMOVED: Readability contribution - was causing undocumented score adjustments
  // Readability should be a separate metric, not part of compliance scoring

  const finalScore = totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;

  // Debug logging for score calculation transparency
  console.log('🔢 [Score Calculation] Level combination details:', {
    level1Score: level1?.score,
    level1Weight: ANALYSIS_WEIGHTS.LEVEL_1_WEIGHT,
    level1Contribution: level1 ? level1.score * ANALYSIS_WEIGHTS.LEVEL_1_WEIGHT : 0,
    level2Score: level2?.score,
    level2Weight: ANALYSIS_WEIGHTS.LEVEL_2_WEIGHT,
    level2Contribution: level2 ? level2.score * ANALYSIS_WEIGHTS.LEVEL_2_WEIGHT : 0,
    level3Score: level3?.score,
    level3Weight: ANALYSIS_WEIGHTS.LEVEL_3_WEIGHT,
    level3Contribution: level3 ? level3.score * ANALYSIS_WEIGHTS.LEVEL_3_WEIGHT : 0,
    totalScore,
    totalWeight,
    finalScore,
  });
  const finalConfidence = confidenceCount > 0 ? Math.round(totalConfidence / confidenceCount) : 0;
  const passed = finalScore >= 70; // 70% threshold for passing

  // Determine severity based on score
  let severity: HipaaSeverity;
  if (finalScore >= 85) {
    severity = HipaaSeverity.INFO;
  } else if (finalScore >= 70) {
    severity = HipaaSeverity.LOW;
  } else if (finalScore >= 50) {
    severity = HipaaSeverity.MEDIUM;
  } else if (finalScore >= 30) {
    severity = HipaaSeverity.HIGH;
  } else {
    severity = HipaaSeverity.CRITICAL;
  }

  return { score: finalScore, passed, confidence: finalConfidence, severity };
}

/**
 * Generate comprehensive remediation guidance
 */
function generateComprehensiveRemediation(
  level1: Level1Result | null,
  level2: Level2Result | null,
  level3: Level3Result | null,
  findings: HipaaFinding[],
): HipaaRemediation {
  const steps: string[] = [];
  const resources: HipaaRemediation['resources'] = [];
  let priority: 'critical' | 'high' | 'medium' | 'low' = 'medium';
  let effort: 'minimal' | 'moderate' | 'significant' | 'extensive' = 'moderate';

  // Analyze findings to determine remediation steps
  const criticalFindings = findings.filter((f) => f.severity === HipaaSeverity.CRITICAL);
  const highFindings = findings.filter((f) => f.severity === HipaaSeverity.HIGH);

  if (criticalFindings.length > 0) {
    priority = 'critical';
    effort = 'significant';
    steps.push('Address critical HIPAA compliance gaps immediately');
  }

  if (highFindings.length > 0) {
    if (priority !== 'critical') priority = 'high';
    steps.push('Resolve high-priority compliance issues');
  }

  // Level 3 specific recommendations
  if (level3?.recommendations) {
    level3.recommendations.forEach((rec) => {
      steps.push(rec.implementation);
    });
  }

  // Add standard resources
  resources.push(
    {
      title: 'HHS HIPAA Privacy Rule Guidance',
      url: 'https://www.hhs.gov/hipaa/for-professionals/privacy/index.html',
      type: 'regulation' as const,
      description: 'Official HIPAA privacy rule guidance from HHS',
    },
    {
      title: 'Sample Privacy Policy Template',
      url: 'https://www.hhs.gov/hipaa/for-professionals/privacy/guidance/privacy-practices-notices/index.html',
      type: 'template' as const,
      description: 'HHS-provided template for privacy practices notices',
    },
  );

  return {
    priority,
    effort,
    steps: steps.length > 0 ? steps : ['Review privacy policy for HIPAA compliance'],
    resources,
    timeline: effort === 'significant' ? '4-6 weeks' : '2-3 weeks',
    estimatedCost: effort === 'significant' ? '$5,000 - $15,000' : '$2,000 - $5,000',
  };
}

/**
 * Collect comprehensive evidence from all analysis levels
 */
function collectComprehensiveEvidence(
  level1: Level1Result | null,
  level2: Level2Result | null,
  level3: Level3Result | null,
  _htmlContent: string,
): HipaaEvidence[] {
  const evidence: HipaaEvidence[] = [];

  // Level 1 evidence
  if (level1?.findings) {
    level1.findings.forEach((finding) => {
      if (finding.type === 'exact_match') {
        evidence.push({
          type: 'text_excerpt' as const,
          content: finding.content,
          location: `Position ${finding.location}`,
          timestamp: new Date().toISOString(),
          relevance: 90,
        });
      }
    });
  }

  // Level 2 evidence
  if (level2?.entities) {
    Object.entries(level2.entities).forEach(([type, entities]) => {
      if (entities.length > 0) {
        evidence.push({
          type: 'metadata' as const,
          content: entities.join(', '),
          location: `${type} entities`,
          timestamp: new Date().toISOString(),
          relevance: 85,
        });
      }
    });
  }

  // Level 3 evidence
  if (level3?.identifiedGaps) {
    level3.identifiedGaps.forEach((gap) => {
      evidence.push({
        type: 'text_excerpt',
        content: gap.description,
        location: gap.requirement,
        timestamp: new Date().toISOString(),
        relevance: 95,
      });
    });
  }

  return evidence;
}

/**
 * Generate comprehensive summary
 */
function generateComprehensiveSummary(result: HipaaCheckResult, _combinedResults: unknown): string {
  const { overallScore, passed, confidence } = result;
  const levelsAnalyzed = Object.keys(result.levelResults || {}).length;

  let summary = `Comprehensive HIPAA analysis completed using ${levelsAnalyzed} analysis level(s). `;
  summary += `Overall compliance score: ${overallScore}%. `;
  summary += `Analysis confidence: ${confidence}%. `;

  if (passed) {
    summary +=
      'Privacy policy demonstrates good HIPAA compliance with minor areas for improvement.';
  } else {
    const criticalIssues = result.details.findings.filter(
      (f) => f.severity === HipaaSeverity.CRITICAL,
    ).length;
    const highIssues = result.details.findings.filter(
      (f) => f.severity === HipaaSeverity.HIGH,
    ).length;

    summary += `Privacy policy has compliance gaps requiring attention: ${criticalIssues} critical and ${highIssues} high-priority issues identified.`;
  }

  return summary;
}

// Helper functions for suggestions
function getPatternSuggestion(requirement: string): string {
  const suggestions: Record<string, string> = {
    'Notice of Privacy Practices header':
      'Add clear "Notice of Privacy Practices" header to your privacy policy',
    'Individual rights section': 'Include detailed section on patient rights under HIPAA',
    'Uses and disclosures': 'Describe how protected health information may be used and disclosed',
    'Contact information': 'Provide contact details for privacy officer and complaint procedures',
  };

  return suggestions[requirement] || 'Review HIPAA requirements for this element';
}

function getNLPSuggestion(findingType: string): string {
  const suggestions: Record<string, string> = {
    entity_found: 'Verify that contact information is current and accessible',
    concept_identified: 'Ensure concept is explained in plain language for patients',
    context_match: 'Review context to ensure accuracy and completeness',
  };

  return suggestions[findingType] || 'Review this element for HIPAA compliance';
}
