import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { Shield, Users, Building, Lock } from 'lucide-react';
import { CategoryResult, RiskLevel } from '@/types/hipaa-security';

interface CategoryBreakdownProps {
  technicalSafeguards: CategoryResult;
  administrativeSafeguards: CategoryResult;
  organizationalSafeguards: CategoryResult;
  physicalSafeguards: CategoryResult;
}

export const CategoryBreakdown: React.FC<CategoryBreakdownProps> = ({
  technicalSafeguards,
  administrativeSafeguards,
  organizationalSafeguards,
  physicalSafeguards,
}) => {
  const getRiskLevelColor = (riskLevel: RiskLevel): string => {
    switch (riskLevel) {
      case 'critical':
        return 'bg-red-500 text-white';
      case 'high':
        return 'bg-orange-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-black';
      case 'low':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const categories = [
    {
      name: 'Technical Safeguards',
      description: 'Technology controls to protect ePHI (§ 164.312)',
      icon: <Shield className="h-6 w-6" />,
      data: technicalSafeguards,
      color: 'blue',
    },
    {
      name: 'Administrative Safeguards',
      description: 'Policies and procedures for workforce (§ 164.308)',
      icon: <Users className="h-6 w-6" />,
      data: administrativeSafeguards,
      color: 'green',
    },
    {
      name: 'Organizational Safeguards',
      description: 'Business associate and organizational requirements (§ 164.314)',
      icon: <Building className="h-6 w-6" />,
      data: organizationalSafeguards,
      color: 'purple',
    },
    {
      name: 'Physical Safeguards',
      description: 'Physical access controls and workstation security (§ 164.310)',
      icon: <Lock className="h-6 w-6" />,
      data: physicalSafeguards,
      color: 'orange',
    },
  ];

  const getScoreColor = (score: number): string => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {categories.map((category) => (
        <Card key={category.name} className="relative overflow-hidden">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg bg-${category.color}-100 text-${category.color}-600`}>
                {category.icon}
              </div>
              <div className="flex-1">
                <CardTitle className="text-lg">{category.name}</CardTitle>
                <p className="text-sm text-gray-600 mt-1">{category.description}</p>
              </div>
              <Badge className={getRiskLevelColor(category.data.riskLevel)}>
                {category.data.riskLevel.toUpperCase()}
              </Badge>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Score Display */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Compliance Score</span>
                <span className={`text-2xl font-bold ${getScoreColor(category.data.score)}`}>
                  {category.data.score}%
                </span>
              </div>
              <Progress value={category.data.score} className="h-2" />
            </div>
            {/* Test Results Summary */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{category.data.passedTests}</div>
                <div className="text-sm text-green-700">Passed</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{category.data.failedTests}</div>
                <div className="text-sm text-red-700">Failed</div>
              </div>
            </div>

            {/* Issue Breakdown */}
            {category.data.failedTests > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">Issues by Severity:</h4>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  {category.data.criticalIssues > 0 && (
                    <div className="flex justify-between">
                      <span>Critical:</span>
                      <Badge variant="destructive" className="text-xs">
                        {category.data.criticalIssues}
                      </Badge>
                    </div>
                  )}
                  {category.data.highIssues > 0 && (
                    <div className="flex justify-between">
                      <span>High:</span>
                      <Badge className="bg-orange-500 text-xs">{category.data.highIssues}</Badge>
                    </div>
                  )}
                  {category.data.mediumIssues > 0 && (
                    <div className="flex justify-between">
                      <span>Medium:</span>
                      <Badge className="bg-yellow-500 text-black text-xs">
                        {category.data.mediumIssues}
                      </Badge>
                    </div>
                  )}
                  {category.data.lowIssues > 0 && (
                    <div className="flex justify-between">
                      <span>Low:</span>
                      <Badge className="bg-green-500 text-xs">{category.data.lowIssues}</Badge>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Special note for Physical Safeguards */}
            {category.name === 'Physical Safeguards' && (
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-xs text-blue-700">
                  <strong>Note:</strong> Physical safeguards require manual audit as they cannot be
                  assessed through external scanning.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
