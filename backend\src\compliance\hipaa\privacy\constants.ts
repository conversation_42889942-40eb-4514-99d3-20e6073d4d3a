// backend/src/compliance/hipaa/constants.ts

/**
 * HIPAA Privacy Policy Compliance Constants
 * Contains patterns, configurations, and requirements for 3-level analysis
 */

// ============================================================================
// LEVEL 1 ANALYSIS - BASIC PHRASE MATCHING PATTERNS
// ============================================================================

/**
 * Required HIPAA sections with their patterns and weights
 */
export const HIPAA_REQUIRED_SECTIONS = {
  NOTICE_HEADER: {
    patterns: [
      /notice\s+of\s+privacy\s+practices/i,
      /privacy\s+notice/i,
      /hipaa\s+privacy\s+notice/i,
      /notice\s+of\s+privacy\s+policies/i,
    ],
    weight: 10,
    description: 'Notice of Privacy Practices header',
    required: true,
  },
  INDIVIDUAL_RIGHTS: {
    patterns: [
      /your\s+rights/i,
      /individual\s+rights/i,
      /patient\s+rights/i,
      /rights\s+under\s+hipaa/i,
    ],
    weight: 9,
    description: 'Individual rights section',
    required: true,
  },
  USES_DISCLOSURES: {
    patterns: [
      /uses?\s+and\s+disclosures?/i,
      /how\s+we\s+use\s+.*\s+information/i,
      /sharing\s+.*\s+information/i,
    ],
    weight: 9,
    description: 'Uses and disclosures section',
    required: true,
  },
  CONTACT_INFO: {
    patterns: [/privacy\s+officer/i, /contact\s+.*\s+privacy/i, /complaints?\s+.*\s+procedure/i],
    weight: 8,
    description: 'Contact information and complaint procedures',
    required: true,
  },
  EFFECTIVE_DATE: {
    patterns: [/effective\s+date/i, /last\s+updated/i, /revision\s+date/i],
    weight: 7,
    description: 'Effective date information',
    required: true,
  },
} as const;

/**
 * Required HIPAA content patterns for Level 1 analysis
 */
export const HIPAA_REQUIRED_CONTENT = {
  PHI_DEFINITION: {
    patterns: [
      /protected\s+health\s+information/i,
      /personal\s+health\s+information/i,
      /\bphi\b/i,
      /health\s+information/i,
      /individually\s+identifiable\s+health\s+information/i,
    ],
    weight: 10,
    description: 'Protected Health Information definition',
    required: true,
    hipaaReference: '45 CFR 164.501',
    exampleLanguage:
      'Protected Health Information (PHI) means individually identifiable health information, including demographic data, that relates to your past, present or future physical or mental health or condition, the provision of health care to you, or payment for health care.',
  },
  USES_DISCLOSURES: {
    patterns: [/treatment/i, /payment/i, /health\s*care\s+operations/i, /healthcare\s+operations/i],
    weight: 9,
    description: 'Treatment, payment, and healthcare operations',
    required: true,
    hipaaReference: '45 CFR 164.506',
    exampleLanguage:
      'We may use and disclose your PHI for: Treatment (providing, coordinating, or managing your health care), Payment (obtaining reimbursement for services), and Healthcare Operations (quality assessment, training, accreditation).',
  },
  INDIVIDUAL_RIGHTS_SPECIFIC: {
    patterns: [
      /right\s+to\s+request\s+restrictions/i,
      /right\s+to\s+inspect\s+and\s+copy/i,
      /right\s+to\s+amend/i,
      /right\s+to\s+accounting\s+of\s+disclosures/i,
      /right\s+to\s+paper\s+copy/i,
      /right\s+to\s+confidential\s+communications/i,
      /right\s+to\s+access/i,
      /right\s+to\s+file\s+a\s+complaint/i,
    ],
    weight: 8,
    description: 'Specific individual rights under HIPAA',
    required: true,
    hipaaReference: '45 CFR 164.520(b)(1)(iv)',
    exampleLanguage:
      'You have the right to: (1) Request restrictions on uses/disclosures, (2) Request confidential communications, (3) Access your PHI, (4) Request amendments, (5) Receive accounting of disclosures, (6) File complaints.',
  },
  COVERED_ENTITY_DUTIES: {
    patterns: [
      /duty\s+to\s+safeguard/i,
      /duty\s+to\s+protect/i,
      /obligation\s+to\s+protect/i,
      /required\s+to\s+protect/i,
      /must\s+protect/i,
      /will\s+protect/i,
    ],
    weight: 7,
    description: 'Covered entity duties and obligations',
    required: true,
    hipaaReference: '45 CFR 164.530(c)',
    exampleLanguage:
      'We are required by law to maintain the privacy and security of your protected health information and to provide you with this notice of our legal duties and privacy practices.',
  },
} as const;

/**
 * Enhanced HIPAA sections with comprehensive patterns and requirements
 */
export const ENHANCED_HIPAA_SECTIONS = {
  AUTHORIZATION_REQUIREMENTS: {
    patterns: [
      /written\s+authorization/i,
      /authorization\s+required/i,
      /signed\s+authorization/i,
      /patient\s+authorization/i,
      /authorization\s+form/i,
      /obtain.*authorization/i,
      /valid\s+authorization/i,
    ],
    weight: 9,
    description: 'Authorization requirements for PHI disclosure',
    required: true,
    hipaaReference: '45 CFR 164.508',
    exampleLanguage:
      'We will obtain your written authorization before using or disclosing your PHI for purposes other than treatment, payment, healthcare operations, or as otherwise permitted by law.',
  },

  MINIMUM_NECESSARY: {
    patterns: [
      /minimum\s+necessary/i,
      /least\s+amount.*information/i,
      /only.*information.*needed/i,
      /limited.*disclosure/i,
      /minimum\s+amount/i,
      /necessary.*purpose/i,
    ],
    weight: 8,
    description: 'Minimum necessary standard explanation',
    required: true,
    hipaaReference: '45 CFR 164.502(b)',
    exampleLanguage:
      'We will limit the use and disclosure of your PHI to the minimum necessary to accomplish the intended purpose of the use or disclosure.',
  },

  BREACH_NOTIFICATION: {
    patterns: [
      /breach\s+notification/i,
      /security\s+incident/i,
      /unauthorized\s+access/i,
      /data\s+breach/i,
      /notify.*breach/i,
      /breach.*notify/i,
      /security\s+breach/i,
    ],
    weight: 7,
    description: 'Breach notification procedures',
    required: false,
    hipaaReference: '45 CFR 164.404',
    exampleLanguage:
      'In the event of a breach of your PHI, we will notify you as required by law within 60 days of discovery of the breach.',
  },

  BUSINESS_ASSOCIATES: {
    patterns: [
      /business\s+associate/i,
      /third.?party.*vendor/i,
      /service\s+provider/i,
      /contractor.*privacy/i,
      /vendor.*agreement/i,
      /business\s+partner/i,
      /subcontractor/i,
    ],
    weight: 8,
    description: 'Business associate relationships and protections',
    required: true,
    hipaaReference: '45 CFR 164.502(e)',
    exampleLanguage:
      'We may disclose your PHI to business associates who perform services on our behalf. These business associates are required to protect your PHI through written agreements.',
  },

  PERMITTED_DISCLOSURES: {
    patterns: [
      /public\s+health/i,
      /health\s+oversight/i,
      /judicial.*proceeding/i,
      /law\s+enforcement/i,
      /research/i,
      /serious\s+threat/i,
      /coroner.*medical\s+examiner/i,
      /organ.*donation/i,
      /workers.*compensation/i,
      /national\s+security/i,
    ],
    weight: 7,
    description: 'Permitted disclosures without authorization',
    required: true,
    hipaaReference: '45 CFR 164.512',
    exampleLanguage:
      'We may use or disclose your PHI without authorization for: public health activities, health oversight, judicial proceedings, law enforcement, research, serious threats to health/safety, and other purposes permitted by law.',
  },

  MARKETING_RESTRICTIONS: {
    patterns: [
      /marketing.*authorization/i,
      /marketing.*consent/i,
      /promotional.*material/i,
      /marketing.*communication/i,
      /opt.*out.*marketing/i,
      /marketing.*restriction/i,
    ],
    weight: 6,
    description: 'Marketing restrictions and authorization requirements',
    required: true,
    hipaaReference: '45 CFR 164.508(a)(3)',
    exampleLanguage:
      'We will not use your PHI for marketing purposes without your written authorization, except for face-to-face communications or promotional gifts of nominal value.',
  },
} as const;

/**
 * General privacy policy patterns for non-healthcare websites
 */
export const GENERAL_PRIVACY_PATTERNS = {
  DATA_COLLECTION: {
    patterns: [
      /collect.*information/i,
      /gather.*data/i,
      /obtain.*personal/i,
      /receive.*information/i,
      /information.*collect/i,
    ],
    weight: 10,
    description: 'Data collection practices',
  },
  DATA_USAGE: {
    patterns: [
      /use.*information/i,
      /process.*data/i,
      /utilize.*personal/i,
      /information.*use/i,
      /data.*process/i,
    ],
    weight: 9,
    description: 'How data is used',
  },
  DATA_SHARING: {
    patterns: [/share.*information/i, /disclose.*data/i, /third.*part/i, /partners/i, /vendors/i],
    weight: 9,
    description: 'Data sharing practices',
  },
  USER_RIGHTS: {
    patterns: [
      /your.*rights/i,
      /user.*rights/i,
      /access.*information/i,
      /delete.*data/i,
      /opt.*out/i,
      /unsubscribe/i,
    ],
    weight: 8,
    description: 'User rights and controls',
  },
  SECURITY: {
    patterns: [/security/i, /protect.*information/i, /safeguard.*data/i, /encryption/i, /secure/i],
    weight: 8,
    description: 'Security measures',
  },
  CONTACT_INFO: {
    patterns: [/contact.*us/i, /privacy.*officer/i, /questions.*privacy/i, /email.*privacy/i],
    weight: 7,
    description: 'Contact information',
  },
} as const;

/**
 * Specific HIPAA compliance checks with detailed requirements
 */
export const HIPAA_SPECIFIC_CHECKS = {
  'HIPAA-NOTICE-HEADER': {
    name: 'Notice of Privacy Practices Header',
    description: 'Validates presence of required HIPAA notice header',
    patterns: [
      /notice\s+of\s+privacy\s+practices/i,
      /hipaa\s+privacy\s+notice/i,
      /privacy\s+notice/i,
    ],
    required: true,
    severity: 'critical' as const,
    hipaaReference: '45 CFR 164.520(b)(1)(i)',
    failureMessage: 'Missing required "Notice of Privacy Practices" header',
    successMessage: 'Proper Notice of Privacy Practices header found',
    recommendation: {
      action: 'Add HIPAA-compliant header to privacy policy',
      implementation: 'Add prominent header at top of privacy policy',
      exampleLanguage:
        'NOTICE OF PRIVACY PRACTICES - This notice describes how medical information about you may be used and disclosed and how you can get access to this information. Please review it carefully.',
      effort: 'minimal' as const,
      impact: 'high' as const,
      timeline: '5 minutes',
    },
  },

  'HIPAA-PHI-DEFINITION': {
    name: 'Protected Health Information Definition',
    description: 'Validates clear definition of PHI',
    patterns: [
      /protected\s+health\s+information/i,
      /\bphi\b.*definition/i,
      /individually\s+identifiable\s+health\s+information/i,
    ],
    required: true,
    severity: 'high' as const,
    hipaaReference: '45 CFR 164.501',
    failureMessage: 'Missing clear definition of Protected Health Information (PHI)',
    successMessage: 'Clear PHI definition found',
    recommendation: {
      action: 'Add comprehensive PHI definition',
      implementation: 'Include definition section explaining what constitutes PHI',
      exampleLanguage:
        'Protected Health Information (PHI) means individually identifiable health information, including demographic data, that relates to your past, present or future physical or mental health or condition, the provision of health care to you, or payment for health care.',
      effort: 'minimal' as const,
      impact: 'high' as const,
      timeline: '10 minutes',
    },
  },

  'HIPAA-INDIVIDUAL-RIGHTS': {
    name: 'Individual Rights Section',
    description: 'Validates comprehensive description of patient rights',
    patterns: [
      /your\s+rights/i,
      /patient\s+rights/i,
      /individual\s+rights/i,
      /you\s+have\s+the\s+right/i,
    ],
    required: true,
    severity: 'critical' as const,
    hipaaReference: '45 CFR 164.520(b)(1)(iv)',
    failureMessage: 'Missing or incomplete individual rights section',
    successMessage: 'Comprehensive individual rights section found',
    recommendation: {
      action: 'Add detailed individual rights section',
      implementation: 'Include all six individual rights with exercise procedures',
      exampleLanguage:
        'You have the right to: (1) Request restrictions on uses/disclosures, (2) Request confidential communications, (3) Access your PHI, (4) Request amendments, (5) Receive accounting of disclosures, (6) File complaints.',
      effort: 'moderate' as const,
      impact: 'critical' as const,
      timeline: '30 minutes',
    },
  },

  'HIPAA-AUTHORIZATION': {
    name: 'Authorization Requirements',
    description: 'Validates explanation of when authorization is required',
    patterns: [/written\s+authorization/i, /authorization\s+required/i, /obtain.*authorization/i],
    required: true,
    severity: 'high' as const,
    hipaaReference: '45 CFR 164.508',
    failureMessage: 'Missing or unclear authorization requirements',
    successMessage: 'Clear authorization requirements found',
    recommendation: {
      action: 'Add authorization requirements section',
      implementation: 'Explain when written authorization is required with examples',
      exampleLanguage:
        'We will obtain your written authorization before using or disclosing your PHI for purposes other than treatment, payment, healthcare operations, or as otherwise permitted by law.',
      effort: 'moderate' as const,
      impact: 'high' as const,
      timeline: '20 minutes',
    },
  },
} as const;

/**
 * All required patterns for Level 1 analysis
 */
export const HIPAA_REQUIRED_PATTERNS = [
  ...Object.values(HIPAA_REQUIRED_SECTIONS).flatMap((section) => section.patterns),
  ...Object.values(HIPAA_REQUIRED_CONTENT).flatMap((content) => content.patterns),
  ...Object.values(ENHANCED_HIPAA_SECTIONS).flatMap((section) => section.patterns),
] as const;

/**
 * General privacy patterns for broader analysis
 */
export const GENERAL_PRIVACY_REQUIRED_PATTERNS = [
  ...Object.values(GENERAL_PRIVACY_PATTERNS).flatMap((section) => section.patterns),
] as const;

// ============================================================================
// LEVEL 2 ANALYSIS - NLP CONFIGURATION
// ============================================================================

/**
 * Configuration for Compromise.js NLP analysis
 */
export const NLP_ANALYSIS_CONFIG = {
  ENTITY_EXTRACTION: {
    extractPeople: true,
    extractOrganizations: true,
    extractDates: true,
    extractPlaces: true,
    extractPhoneNumbers: true,
    extractEmails: true,
  },
  CONTEXT_ANALYSIS: {
    sentenceWindow: 3, // Analyze 3 sentences around key phrases
    confidenceThreshold: 0.7,
    requireMultipleMatches: true,
    minContextLength: 50, // Minimum characters for context
  },
  PRIVACY_CONCEPTS: {
    protection: ['protect', 'safeguard', 'secure', 'confidential'],
    disclosure: ['share', 'disclose', 'release', 'provide'],
    usage: ['use', 'utilize', 'process', 'handle'],
    rights: ['right', 'entitle', 'authorize', 'permit'],
  },
} as const;

// ============================================================================
// LEVEL 3 ANALYSIS - AI CONFIGURATION
// ============================================================================

/**
 * Configuration for DistilBERT AI analysis
 */
export const AI_ANALYSIS_CONFIG = {
  MODEL_CONFIG: {
    modelName: 'distilbert-base-uncased',
    maxLength: 512,
    batchSize: 1,
    confidenceThreshold: 0.8,
    temperature: 0.1, // Low temperature for consistent results
  },
  COMPLIANCE_ANALYSIS: {
    gapDetectionEnabled: true,
    riskAssessmentEnabled: true,
    recommendationGeneration: true,
    legalAnalysisEnabled: true,
  },
  PROCESSING_LIMITS: {
    maxTextLength: 50000, // Maximum characters to analyze
    timeoutMs: 30000, // 30 seconds timeout
    retryAttempts: 2,
  },
} as const;

// ============================================================================
// SCORING AND WEIGHTS
// ============================================================================

/**
 * Weights for combining results from all 3 analysis levels
 */
export const ANALYSIS_WEIGHTS = {
  LEVEL_1_WEIGHT: 0.3, // 30% - Basic phrase matching
  LEVEL_2_WEIGHT: 0.35, // 35% - NLP understanding
  LEVEL_3_WEIGHT: 0.35, // 35% - AI analysis

  MINIMUM_PASSING_SCORE: 70, // 70% overall to pass
  HIGH_CONFIDENCE_THRESHOLD: 85, // 85% for high confidence
  CRITICAL_THRESHOLD: 50, // Below 50% is critical
} as const;

/**
 * Weights for combining different HIPAA checks
 * Based on HIPAA regulatory importance and compliance impact
 */
export const CHECK_WEIGHTS = {
  'HIPAA-PP-001': 0.15, // 15% - Privacy Policy Presence (threshold requirement)
  'HIPAA-COMPREHENSIVE-001': 0.7, // 70% - Comprehensive Analysis (core compliance)
  'HIPAA-CONTACT-001': 0.15, // 15% - Contact Information (procedural requirement)
} as const;

/**
 * Industry-specific thresholds for different website types
 */
export const INDUSTRY_THRESHOLDS = {
  healthcare: {
    passing: 75, // Higher standard for healthcare entities
    good: 85,
    excellent: 95,
    critical: 40,
  },
  general: {
    passing: 60, // Lower for general websites
    good: 75,
    excellent: 85,
    critical: 30,
  },
} as const;

/**
 * Severity scoring thresholds
 */
export const SEVERITY_THRESHOLDS = {
  CRITICAL: { min: 0, max: 30 },
  HIGH: { min: 31, max: 50 },
  MEDIUM: { min: 51, max: 70 },
  LOW: { min: 71, max: 85 },
  INFO: { min: 86, max: 100 },
} as const;

// ============================================================================
// READABILITY AND QUALITY STANDARDS
// ============================================================================

/**
 * Plain language and readability requirements
 */
export const READABILITY_STANDARDS = {
  FLESCH_SCORE: {
    MINIMUM: 60, // 8th grade reading level
    PREFERRED: 70, // 7th grade reading level
    EXCELLENT: 80, // 6th grade reading level
  },
  COMPLEX_TERMS: {
    MAX_ALLOWED: 20, // Maximum complex terms per policy
    ALTERNATIVES: {
      aforementioned: 'mentioned above',
      heretofore: 'until now',
      notwithstanding: 'despite',
      'pursuant to': 'according to',
      whereas: 'while',
      thereof: 'of it',
      herein: 'in this document',
    },
  },
  SENTENCE_LENGTH: {
    MAX_AVERAGE: 20, // Maximum average words per sentence
    MAX_SINGLE: 35, // Maximum words in a single sentence
  },
} as const;

// ============================================================================
// PERFORMANCE AND CACHING
// ============================================================================

/**
 * Performance optimization settings
 */
export const PERFORMANCE_CONFIG = {
  CACHE_TTL: {
    PAGE_CONTENT: 3600, // 1 hour
    SCAN_RESULTS: 86400, // 24 hours
    PATTERNS: -1, // Never expire
  },
  TIMEOUTS: {
    PAGE_FETCH: 30000, // 30 seconds
    ANALYSIS_LEVEL_1: 5000, // 5 seconds
    ANALYSIS_LEVEL_2: 15000, // 15 seconds
    ANALYSIS_LEVEL_3: 30000, // 30 seconds
    TOTAL_SCAN: 60000, // 60 seconds total
  },
  CONCURRENCY: {
    MAX_PARALLEL_CHECKS: 5,
    MAX_PARALLEL_SCANS: 10,
  },
} as const;

// ============================================================================
// ERROR CODES AND MESSAGES
// ============================================================================

/**
 * Standard error codes for HIPAA compliance checks
 */
export const ERROR_CODES = {
  INVALID_URL: 'HIPAA_INVALID_URL',
  FETCH_FAILED: 'HIPAA_FETCH_FAILED',
  PARSE_ERROR: 'HIPAA_PARSE_ERROR',
  TIMEOUT: 'HIPAA_TIMEOUT',
  ANALYSIS_FAILED: 'HIPAA_ANALYSIS_FAILED',
  VALIDATION_ERROR: 'HIPAA_VALIDATION_ERROR',
  RATE_LIMITED: 'HIPAA_RATE_LIMITED',
  INSUFFICIENT_CONTENT: 'HIPAA_INSUFFICIENT_CONTENT',
} as const;

/**
 * User-friendly error messages
 */
export const ERROR_MESSAGES = {
  [ERROR_CODES.INVALID_URL]: 'The provided URL is not valid or accessible',
  [ERROR_CODES.FETCH_FAILED]: 'Unable to fetch the webpage content',
  [ERROR_CODES.PARSE_ERROR]: 'Unable to parse the webpage content',
  [ERROR_CODES.TIMEOUT]: 'The analysis took too long to complete',
  [ERROR_CODES.ANALYSIS_FAILED]: 'The compliance analysis encountered an error',
  [ERROR_CODES.VALIDATION_ERROR]: 'The request contains invalid parameters',
  [ERROR_CODES.RATE_LIMITED]: 'Too many requests. Please try again later',
  [ERROR_CODES.INSUFFICIENT_CONTENT]: 'Not enough content found to perform analysis',
} as const;
